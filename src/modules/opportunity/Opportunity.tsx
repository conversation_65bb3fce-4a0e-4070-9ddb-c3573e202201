import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom'
import { Form, Formik } from 'formik'
import * as Yup from 'yup'

import ImageIcon from '../../assets/newIcons/media.png'

import { getCampaigns, getLeadSources } from '../../logic/apis/leadSource'
import { getPosition } from '../../logic/apis/position'
import { getOrder, getProject, getProjectTypes, getTax } from '../../logic/apis/projects'
import {
  deleteOpportunity,
  getCheckpoint,
  getDisplaySteps,
  getOpportunityActivity,
  getOpportunityById,
  getPositionMembersById,
  getSalesActions,
  getStages,
  updateActivity,
  updateOppStatusApi,
  updateOpportunity,
  getStepChecklist,
  getPONum,
  getOpportunityModifiedCommission,
  restoreOpportunity,
} from '../../logic/apis/sales'

import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'
import Button from '../../shared/components/button/Button'
import { RoundButton } from '../../shared/components/button/style'
import { CustomModal } from '../../shared/customModal/CustomModal'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { EditSvg } from '../../shared/helpers/images'
import {
  dayjsFormat,
  extractPoInfo,
  formatDateymd,
  formatNumberToCommaS,
  formatPhoneNumber,
  getIdFromName,
  getKeysFromObjects,
  getNameFromId,
  getSalesPersonIdFromName,
  getSalesPersonNameFromId,
  getStageIdFromName,
  getStageNameFromId,
  getTaxJurisdictionIdFromName,
  getTaxJurisdictionNameFromId,
  getNameFrom_Id,
  isSuccess,
  notify,
  simplifyBackendError,
  truncateParagraph,
  getValueByKeyAndMatch,
  getEnumValue,
  extractPermissionByName,
  hasValues,
  roundToNearestTenth,
  getDataFromLocalStorage,
} from '../../shared/helpers/util'
import useWindowDimensions from '../../shared/hooks/useWindowDimensions'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { NormalInput } from '../../shared/normalInput/NormalInput'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { colors } from '../../styles/theme'
import { I_Checkpoint } from '../crmSettings/CrmSettings'
import { I_LeadSource, I_Position, I_SalesPerson } from '../newLead/NewLead'
import NewProject from '../newProject/NewProject'
import ActionModal from './components/actionModal/ActionModal'
import Activity from './components/activity/Activity'
import AssessmentForm, { AnyKey, I_Action, I_Stage } from './components/assessmentForm/AssessmentForm'
import Comments from './components/comments/Comments'
import DailyLogBox from './components/dailyLog/DailyLogBox'
import Dates from './components/dates/Dates'
import DeletedProjects from './components/deletedProjects/DeletedProjects'
import LostModal from './components/lostModal/LostModal'
import * as Styled from './styles'
import {
  FinanceFeeVisibleTo,
  FormAccess,
  Nue,
  StageGroupEnum,
  StorageKey,
  usStatesShortNames,
} from '../../shared/helpers/constants'
import { getCompanyCrews } from '../../logic/apis/crew'
import { getSubContractors } from '../../logic/apis/subcontractor'
import { getReferres, getSalesPersonAndPM } from '../../logic/apis/company'
import ErrorBoundary from '../app/errorBoundary/ErrorBoundary'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { NewTableBoldValue, NewTableValue } from '../../shared/table/style'
import SalesCommission from './components/salesCommission/Salescommission'
import DriveTimeModal from './components/driveTimeModal/DriveTimeModal'
import { I_Contacts, mergeSourceAndCampaignNames } from '../client/components/addNewClientModal/AddNewClientModal'
import CommissionModal from './components/commissionModal/CommissionModal'
import { getLatLongFromAddress } from '../../shared/helpers/map'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../config'
import { SLoader } from '../../shared/components/loader/Loader'
import { getDepartments } from '../../logic/apis/department'
import ToDoNext from './components/toDoNext/ToDoNext'
import FormSelect from '../../shared/formSelect/FormSelect'
import { BoxGap, BoxSpacing, PrintNotesContainer } from '../orderDetails/styles'
import ReactMarkdown from 'react-markdown'
import { renderColors, renderCrew, sortInventoryMats, sortTasks } from '../orderDetails/OrderDetails'
import { getFormattedLeadSrcData, getLeadSrcDropData, getLeadSrcDropdownId } from '../leadSource/LeadSource'
import useFetch from '../../logic/apis/useFetch'
import AutoCompleteIndentation from '../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { getContactById, getLinkedContact, updateContact } from '../../logic/apis/contact'
import ToDoNextProfile from '../contact/components/contactProfile/components/ToDoNextProfile'
import SearchableDropdown from '../../shared/searchableDropdown/SearchableDropdown'
import { fetchSearchReferer } from '../contact/components/contactProfile/ContactProfile'

const materialTable = (materialsProps: any, originalMatList?: boolean, printFlag?: boolean) => {
  const originalMatListFlag = !!originalMatList
  return (
    <BoxSpacing margin={'0 0 14px 40px'}>
      <BoxGap width="80%">
        <BoxGap width="100%" display="flex" border={true}>
          <BoxGap className="three-table-div1" width={`${false ? '60%' : '80%'}`}>
            <SharedStyled.Text fontSize="14px">
              <b>Material</b>
            </SharedStyled.Text>
          </BoxGap>
          <BoxGap textAlign="center" className="three-table-div2" width="10%">
            <SharedStyled.Text fontSize="14px">
              <b>Amt</b>
            </SharedStyled.Text>
          </BoxGap>
          <BoxGap textAlign="center" className="three-table-div3" width="10%">
            <SharedStyled.Text fontSize="14px">
              <b>Unit</b>
            </SharedStyled.Text>
          </BoxGap>
          {/* {materialToggle && (
            <>
              <Styled.BoxGap textAlign="center" className="three-table-div2" width="10%">
                <SharedStyled.Text fontSize="14px">
                  <b>Price</b>
                </SharedStyled.Text>
              </Styled.BoxGap>
              <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                <SharedStyled.Text fontSize="14px">
                  <b>Total</b>
                </SharedStyled.Text>
              </Styled.BoxGap>
            </>
          )} */}
        </BoxGap>
      </BoxGap>
      {materialsProps &&
        materialsProps
          ?.filter((v) => !(!!v.deleted && printFlag))
          ?.filter(
            ({ name, unit, amount }: { name: string; unit: string; amount: number }) =>
              !originalMatListFlag || (name && unit && amount != null)
          )
          // ?.filter((v) => {
          //   if (!!v.deleted && printFlag) return false
          //   else return true
          // })
          // ?.filter(({ name, unit, amount }: { name: string; unit: string; amount: number }) => {
          //   if (!originalMatListFlag) return true // Skip the filter if the condition is false
          //   return name && unit && amount != null // Apply the filter logic
          // })
          ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))
          ?.map(
            (
              {
                name,
                unit,
                amount,
                cost,
                nameEdit,
                unitEdit,
                amountEdit,
                deleted,
              }: {
                name: string
                unit: string
                amount: number
                cost: number
                nameEdit: string
                unitEdit: string
                amountEdit: number
                deleted: boolean
              },
              index: number
            ) => {
              return (
                <BoxGap width="80%" key={index}>
                  <BoxGap width="100%" display="flex" border={true}>
                    <BoxGap className="three-table-div1" width={`${false ? '60%' : '80%'}`}>
                      <SharedStyled.Text
                        textDecoration={
                          (deleted !== undefined ? deleted : false) && !originalMatListFlag ? 'line-through' : 'unset'
                        }
                        fontSize="14px"
                      >
                        {!originalMatListFlag ? nameEdit || name : name}
                      </SharedStyled.Text>
                    </BoxGap>
                    <BoxGap textAlign="right" className="three-table-div2" width="10%">
                      <SharedStyled.Text
                        textDecoration={
                          (deleted !== undefined ? deleted : false) && !originalMatListFlag ? 'line-through' : 'unset'
                        }
                        fontSize="14px"
                      >
                        {roundToNearestTenth(!originalMatListFlag ? amountEdit || amount : amount)?.toFixed(1)}
                        {/* <SharedStyled.Text fontSize="14px">{roundToNearestTenth(amount)?.toFixed(1)}</SharedStyled.Text> */}
                      </SharedStyled.Text>
                    </BoxGap>
                    <BoxGap textAlign="center" className="three-table-div3" width="10%">
                      <SharedStyled.Text
                        textDecoration={
                          (deleted !== undefined ? deleted : false) && !originalMatListFlag ? 'line-through' : 'unset'
                        }
                        fontSize="14px"
                      >
                        {!originalMatListFlag ? unitEdit || unit : unit}
                      </SharedStyled.Text>
                    </BoxGap>
                    {/* {materialToggle && (
                      <>
                        <BoxGap textAlign="right" className="three-table-div2" width="10%">
                          <SharedStyled.Text
                            textDecoration={
                              (deleted !== undefined ? deleted : false) && !originalMatListFlag
                                ? 'line-through'
                                : 'unset'
                            }
                            fontSize="14px"
                          >
                            ${formatNumberToCommaS(cost / amount) || '--'}
                          </SharedStyled.Text>
                        </BoxGap>
                        <BoxGap textAlign="right" className="three-table-div3" width="10%">
                          <SharedStyled.Text
                            textDecoration={
                              (deleted !== undefined ? deleted : false) && !originalMatListFlag
                                ? 'line-through'
                                : 'unset'
                            }
                            fontSize="14px"
                          >
                            ${formatNumberToCommaS(cost) || '--'}
                          </SharedStyled.Text>
                        </BoxGap>
                      </>
                    )} */}
                  </BoxGap>
                </BoxGap>
              )
            }
          )}
    </BoxSpacing>
  )
}

export interface I_NextAction {
  _id: string
  type: string
  body: string
  due: string
  createdBy: string
  assignTo?: string
  createdAt: string
}

const hasValueChanged = (initialValue: string | number, finalValue: string | number) => {
  return initialValue !== finalValue
}

export interface I_Opportunity {
  _id: string
  oppType: string
  contactId: string
  PO: string
  num: string
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: string
  leadSource: string
  leadCost: string
  distance: number
  salesPerson: string
  stage: string
  comments: Array<any>
  lost: boolean
  leadDate: string
  createdBy: string
  deleted: boolean
  type: string
  oppNotes: string
  client: any
  addrCityState: string
  createdAt: string
  updatedAt: string
  actions?: I_Action[]
  nextAction?: I_NextAction
  [key: string]: any
}

export interface I_Client {
  _id: string
  isBusiness: boolean
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: string
  phone: string
  leadSource: string
  leadSourceName: string
  notes: string
  createdBy: string
  deleted: boolean
  createdAt: string
  updatedAt: string
}

export interface I_SalesActions {
  _id: string
  action: string
  type: string
  deleted: false
  createdBy: string
  createdAt: string
  updatedAt: string
}
interface StepObject {
  [key: string]: boolean
}

const renderAllSteps = (allSteps: any, bool: boolean) => {
  if (!allSteps) return null

  return Object.entries(allSteps).map(([key, value]) => {
    if (value && value !== '0') {
      let displayValue

      if (typeof value === 'string') {
        if (/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/.test(value)) {
          displayValue = dayjsFormat(value, 'M/D/YY')
        } else {
          displayValue = value
        }
      } else if (typeof value === 'object') {
        displayValue = dayjsFormat(value?.created, 'M/D/YY')
      } else {
        displayValue = value
      }
      // else if (typeof value === 'number') {
      //   displayValue = `$${formatNumberToCommaS(value)}`
      // }

      if (bool) {
        return key?.toLowerCase()?.includes('permit') ? (
          <SharedStyled.FlexRow key={key} flexDirection="row-reverse" justifyContent="space-between">
            <p>{key}</p>
            <span className="bold">{displayValue}</span>
          </SharedStyled.FlexRow>
        ) : null
      }
      return (
        <SharedStyled.FlexRow key={key} flexDirection="row-reverse" justifyContent="space-between">
          <p>{key}</p>
          <span className="bold">{displayValue}</span>
        </SharedStyled.FlexRow>
      )
    }
    return null
  })
}

const Opportunity = () => {
  const { oppId } = useParams()
  const { navCollapsed } = useAppSelector((state) => state.ui)
  const location = useLocation()
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companySettingForAll, positionDetails, positionPermissions } =
    globalSelector.company

  const operationsFlag = location.pathname.includes('operations')
  const deletedFlag = location.state?.isDeleted || false

  const [restoreLoading, setRestoreLoading] = useState(false)
  // client and opportunity data
  const [loading, setLoading] = useState(false)
  const [curStage, setCurStage] = useState<any>({})
  const [updateLoading, setUpdateLoading] = useState(false)
  const [oppData, setOppData] = useState<I_Opportunity | undefined>()
  const [lostModal, setLostModal] = useState(false)
  const [showDriveTimeModal, setShowDriveTimeModal] = useState(false)
  const [isInactive, setIsInactive] = useState(false)
  const [isLost, setIsLost] = useState(false)
  const [inputKeys, setInputKeys] = useState<AnyKey>({})
  const [btnLoading, setBtnLoading] = useState(false)
  const [toggleAddress, setToggleAddress] = useState(false)
  const [commissionUpdateBool, setCommissionUpdateBool] = useState(false)

  const [clientData, setClientData] = useState<I_Client | undefined>()
  const [clientLoading, setClientLoading] = useState(false)
  // const [projectManagerCheckbox, setProjectManagerCheckbox] = useState(false)
  const [leadSrcDrop, setLeadsrcDrop] = useState<I_LeadSource[]>([])
  const [leadSrcData, setLeadSrcData] = useState([])

  const [cityDropdown, setCityDropdown] = useState<string[]>([])
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [salesManagerDrop, setSalesManagerDrop] = useState<I_SalesPerson[]>([])
  const [stages, setStages] = useState<I_Stage[]>([])
  const [allStages, setAllStages] = useState<I_Stage[]>([])
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  // const [stepForToDoNext, setStepForToDoNext] = useState<any>([])
  const [stepForToDoNextFlag, setStepForToDoNextFLag] = useState(true)
  const [stepForToDoNextSuccessFlag, setStepForToDoNextSuccessFLag] = useState(false)
  const [getAtivity, setActivity] = useState<any>([])
  const [activityLoading, setActivityLoading] = useState(false)
  const [projectTypes, setProjectTypes] = useState([])
  const [salesCommissionData, setSalesCommissionData] = useState([])
  const [projectManagerName, setProjectManagerName] = useState('')
  const [duration, setDuration] = useState(0)
  const [refererres, setRefererres] = useState<any>([])
  const [projectLoading, setProjectLoading] = useState(false)
  const [addCommission, setAddCommission] = useState(false)
  const [officeDrop, setOfficeDrop] = useState<any[]>([])

  // projects data
  const [pageCount, setPageCount] = useState<number>(1)

  // checkpoint
  const [cpLoading, setCpLoading] = useState(false)
  const [checkpoints, setCheckpoints] = useState<I_Checkpoint[]>([])
  const [projectTableValues, setProjectTableValues] = useState([])
  const [contractOrder, setContractOrder] = useState<any>([])
  const [tax, setTax] = useState<any>([])
  const [formatedTax, setFormatedTax] = useState<any>([])
  const [taxUniqueState, setTaxUniqueState] = useState<any>([])
  const [checkListData, setCheckListData] = useState<any>()
  const [allSteps, setAllSteps] = useState<any>([])

  const [distance, setDistance] = useState(0)
  const [selectedType, setSelectedType] = useState(oppData?.type)
  const [steps, setSteps] = useState<any>([]) // only parent steps

  const [editing, setEditing] = useState(false)
  const [showAddProjectModal, setShowAddProjectModal] = useState(false)
  const { width } = useWindowDimensions()
  const [stepObject, setStepObject] = useState<StepObject>({})
  const [crewSub, setCrewSub] = useState<any>([])
  const [inputValue, setInputValue] = useState('')
  const [commissionEdit, setCommissionEdit] = useState(false)
  const [commissionUpdate, setCommissionUpdate] = useState({})

  const [material1, setMaterial1] = useState<any>([])
  const [material2, setMaterial2] = useState<any>([])
  const [poNum, setPoNum] = useState({ po: '', num: '' })
  const [linkedContactData, setLinkedContactData] = useState<any>([])
  const [linkedContactLoading, setLinkedContactLoading] = useState(true)
  const [addressInputType, setAddressInputType] = useState<'custom' | 'google'>()

  const isOppInfoPermissionReadOnly =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'opportunity info')?.crud?.write === false

  {
    /* hardcoded code for hiding the UI for subcontractor position  */
  }
  const isRestrictedOpp = positionDetails?.symbol === 'subContractor' || positionDetails?.symbol === 'Foreman'

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])

  const columns = [
    {
      Header: 'Name',
      accessor: (row: any) => {
        return (
          <>
            <NewTableBoldValue>{row?.name}</NewTableBoldValue>
            <NewTableValue className="smallText">{getNameFromId(row.projectType, projectTypesDrop)}</NewTableValue>
          </>
        )
      },
    },
    {
      Header: 'Project Type',
      accessor: (row: any) => {
        return (
          <>
            <NewTableValue className="smallText">{dayjsFormat(row.createdAt, 'M/D/YY')}</NewTableValue>
            <NewTableValue className="smallText">{`${row?.user?.[0]?.firstName || '--'} ${
              row?.user?.[0]?.lastName || '--'
            }`}</NewTableValue>
          </>
        )
      },
    },

    {
      Header: 'Action',
      // accessor: (row: any) => `${row?._id}`,
      accessor: (row: any) => (
        // !row?.orderId && (
        <RoundButton
          title="Edit Project"
          onClick={(event) => {
            event.stopPropagation()
            navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/edit-project/${row?._id}`)
          }}
        >
          <img src={EditSvg} alt="edit icon" />
        </RoundButton>
      ),
      // ),

      // Cell: ({ value }: { value: string }) => (
      //   <SharedStyled.IconContainer
      //     className="edit"
      //     onClick={(event) => {
      //       event.stopPropagation()
      //       navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/edit-project/${value}`)
      //     }}
      //   >
      //     <EditIcon />
      //   </SharedStyled.IconContainer>
      // ),
    },
  ]

  const initClientData = {
    dateReceived: dayjsFormat(oppData?.newLeadDate, 'YYYY-MM-DDTHH:mm') ?? '',
    oppDateReceived: dayjsFormat(oppData?.oppDate, 'YYYY-MM-DDTHH:mm') ?? '',
    leadSource: oppData?.leadSourceId
      ? // ? getNameFrom_Id(oppData?.leadSourceId, leadSrcDrop) || oppData?.leadSource
        oppData?.campaignName || oppData?.leadSource
      : oppData?.leadSource || '',
    // leadSource: oppData?.leadSource ?? '',
    referredBy: oppData?.referredBy ? getSalesPersonNameFromId(oppData?.referredBy, refererres) : '',
    workingCrew: oppData?.workingCrew?.name ?? '',
    distance: oppData?.distance ?? 0,
    tempDistance: oppData?.distance ?? 0,
    duration: oppData?.duration ?? 0,
    financeFee: oppData?.financeFee ?? 0,
    oppCity: oppData?.city ?? '',
    oppState: oppData?.state ?? '',
    zip: oppData?.zip ?? '',
    street: oppData?.street ?? '',
    stage: getStageNameFromId(oppData?.stage!, stages) ?? '',
    type: oppData ? getNameFromId(oppData.oppType, projectTypesDrop) : '',
    csr: oppData?.csrId ? getNameFrom_Id(oppData.csrId, officeDrop) || oppData?.CSRAssigned : '',
    assignedTo: oppData?.salesPerson
      ? getSalesPersonNameFromId(oppData?.salesPerson, salesPersonDrop) || oppData?.salesPersonName
      : '',
    projectManager: oppData?.projectManager
      ? getSalesPersonNameFromId(oppData?.projectManager, salesManagerDrop) || oppData?.projectManagerName
      : '',
    // soldValue: '',
    // discount: '',
    taxJurisdiction: oppData ? getTaxJurisdictionNameFromId(oppData.taxJurisdiction, formatedTax) : '',
    city: '',
    state: '',
  }

  const userIdLocal: any = JSON.parse(localStorage.getItem('id') || '')

  const validationSchema = Yup.object().shape({
    oppState: Yup.string().matches(/^[A-Z]{2}$/, 'State must be two uppercase letters (e.g., WA)'),
  })

  // oppData ? getNameFromId(oppData.oppType, projectTypesDrop) : ''
  useEffect(() => {
    if (oppData && stages?.length) {
      const stage: I_Stage | undefined = findCurrStage(oppData, stages)
      if (stage) {
        setCurStage(stage)
      }
    }
  }, [oppData, stages])

  useEffect(() => {
    // Ensure oppData and salesManagerDrop are available before setting projectManagerName
    if (oppData?.projectManager && salesManagerDrop) {
      const managerName = getSalesPersonNameFromId(oppData.projectManager, salesManagerDrop)
      setProjectManagerName(managerName)
    }
  }, [oppData?.projectManager, salesManagerDrop])

  useEffect(() => {
    if (oppId && !isRestrictedOpp) {
      fetchActivity()
    }
  }, [oppId])

  // useEffect(() => {
  //   if (projectManagerCheckbox) {
  //     handleClientSubmit(initClientData)
  //   }
  // }, [projectManagerCheckbox])

  useEffect(() => {
    initFetch()
  }, [operationsFlag, oppId])

  useEffect(() => {
    if (oppData) {
      // setIsLost(oppData.lost)
      setIsInactive(oppData.status && oppData.status === 'inactive' ? true : false)
    }
  }, [oppData])

  useEffect(() => {
    const stageName = getStageNameFromId(oppData?.stage!, stages)
    if (oppId && stageName !== '' && !isRestrictedOpp) {
      fetchUpdateStepChecklist()
    }
  }, [oppId, stages, oppData?.stage])

  useEffect(() => {
    initFetchProjectType()
    fetchOrder()
    !isRestrictedOpp && fetchTax()
    fetchDisplaySteps()
    !isRestrictedOpp && fetchCompanyCrewsAndSubs()
    !isRestrictedOpp && initFetchReferrers()
    !isRestrictedOpp && getPositionsOffice()
  }, [])

  const getPositionsOffice = async () => {
    try {
      const response = await getDepartments({ deleted: false }, false)
      if (isSuccess(response)) {
        console.log({ response })
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionMembersForOffice(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionMembersForOffice = async (departmentId: string) => {
    try {
      // const response = await getPositionMembersById({ departmentId }, false) // NHR-1567

      const response = await getSalesPersonAndPM(departmentId) // NHR-1567
      if (isSuccess(response)) {
        // setOfficeDrop(response?.data?.data?.memberData) // NHR-1567
        setOfficeDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  useEffect(() => {
    if (oppId) {
      !isRestrictedOpp && fetchOpportunityModifiedCommission()
    }
  }, [oppId, commissionUpdateBool])

  const fetchOpportunityModifiedCommission = async () => {
    try {
      const res = await getOpportunityModifiedCommission(oppId!)
      if (isSuccess(res)) {
        setSalesCommissionData(res?.data?.data?.oppComm)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, false)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const fetchActivity = async () => {
    try {
      setActivityLoading(true)
      const res = await getOpportunityActivity(oppId!)
      if (isSuccess(res)) {
        const { oppActivities } = res?.data?.data

        setActivity(oppActivities)
      }
    } catch (error) {
      console.log(error)
    } finally {
      setActivityLoading(false)
    }
  }
  console.log({ oppData })
  const fetchCompanyCrewsAndSubs = async () => {
    try {
      const apiCalls = [
        await getCompanyCrews({ retired: false, deleted: false }),
        await getSubContractors({
          limit: '100',
          deleted: false,
          retired: false,
        }),
      ]
      const [responseCrews, responseSubs] = await Promise.all(apiCalls)

      if (responseCrews?.status >= 200 && responseSubs?.status >= 200) {
        const { crew = [] } = responseCrews?.data?.data || {}
        const { subcontractor = [] } = responseSubs?.data?.data || {}
        const finalValues: any[] = [
          ...crew.map((crew) => ({ name: crew.name, id: crew._id })),
          ...subcontractor?.filter((v) => v.isActive === true)?.map((sub) => ({ name: sub.name, id: sub._id })),
        ]
        setCrewSub(finalValues)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const findCurrStage = (oppData: I_Opportunity | AnyKey, stages: I_Stage[]) => {
    let curStage: I_Stage | undefined
    stages.forEach((stage) => {
      if (stage._id === oppData.stage) {
        curStage = stage
        return
      }
    })
    return curStage
  }

  const initFetchProjectType = async () => {
    try {
      // setLoading(false)
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypes(projectType)
        setProjectTypesDrop(object)
        // setLoading(true)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
      // setLoading(true)
    }
  }
  const fetchDisplaySteps = async () => {
    try {
      const res = await getDisplaySteps({ oppId: oppId! })

      if (isSuccess(res)) {
        const { finalStep } = res?.data?.data
        setAllSteps(finalStep)
      }
    } catch (error) {
      console.log('get all step fetch failed!', error)
    }
  }
  const fetchTax = async () => {
    try {
      const res = await getTax()
      if (isSuccess(res)) {
        const { tax } = res?.data?.data
        setTaxUniqueState([...new Set(tax.map((item: any) => item.state))])
        setTax(tax)
        console.log({ firstasdds: [...new Set(tax.map((item: any) => item.state))], tax })
        const formateTax = tax.map((obj: any) => ({
          ...obj,
          identifier: `${obj?.name} ${
            obj?.rate !== undefined && obj?.rate !== null && !isNaN(obj?.rate) && obj?.rate > 0 ? `(${obj.rate}%)` : ''
          }`,
        }))
        setFormatedTax(formateTax)
        // let uniqueStates = [...new Set(tax.map((item:any) => item.state))];
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('get tax fetch failed!', error)
    }
  }
  const fetchOrder = async () => {
    try {
      const res = await getOrder(false, oppId)
      if (isSuccess(res)) {
        const { Order } = res?.data?.data

        const currentOrder = Order?.[0]

        var inventoryMats = []
        for (var i = 0; i < currentOrder?.matList.length; i++) {
          if (currentOrder?.matList[i].inventory === true) {
            inventoryMats.push(currentOrder?.matList[i])
          }
        }
        var mats1 = []
        for (var i = 0; i < currentOrder?.matList.length; i++) {
          if (currentOrder?.matList[i].inventory === false) {
            mats1.push(currentOrder?.matList[i])
          }
        }

        inventoryMats = sortInventoryMats(inventoryMats)
        mats1 = sortInventoryMats(mats1)

        setMaterial1(inventoryMats)
        setMaterial2(mats1)

        setContractOrder(Order)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('order fetch failed!', error)
    }
  }

  const fetchProject = async () => {
    setProjectLoading(true)
    try {
      const res = await getProject({ deleted: false, opportunityId: oppId }, 0)
      if (isSuccess(res)) {
        const { project } = res?.data?.data

        const tableData = project.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              createdBy: cur.createdBy,
              createdAt: cur.createdAt,
              id: cur._id,
            },
          ]
        }, [])
        setProjectTableValues(tableData)
        setProjectLoading(false)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('project fetch failed!', error)
      setProjectLoading(false)
    }
  }

  const makeInactive = async () => {
    try {
      const response = await updateOppStatusApi({
        opportunityId: oppId!,
        status: isInactive ? 'active' : 'inactive',
      })
      if (isSuccess(response)) {
        setIsInactive((prev) => !prev)
        notify('Status changed!', 'success')
      } else throw new Error(response?.data?.message)
    } catch (err) {
      notify(`Failed to update status!`, 'error')
      console.log('makeInactive err', err)
    }
  }

  const makeLost = async () => {
    setLostModal(true)
  }

  const fetchUpdateStepChecklist = async () => {
    setStepForToDoNextFLag(true)
    // const stageName = getStageNameFromId(oppData?.stage!, stages)
    try {
      const res = await getStepChecklist(oppData?.stage!, oppId!)

      if (isSuccess(res)) {
        setCheckListData(res?.data?.data?.checklist?.stepsChecklist?.[oppData?.stage])
        setStepForToDoNextFLag(false)
      }
    } catch (error) {
      setStepForToDoNextFLag(false)
      console.log(error)
    }
  }

  const getContact = async (contactId: string) => {
    try {
      const clientRes = await getContactById(contactId!)
      if (isSuccess(clientRes)) {
        setClientData(clientRes?.data?.data?.client)
      } else throw new Error(clientRes?.data?.message)
    } catch (err) {
      console.log('CLIENT ERR', err)
    }
  }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  const fetchCheckpoints = async () => {
    setCpLoading(true)
    try {
      const response = await getCheckpoint(false, operationsFlag ? StageGroupEnum.Operations : StageGroupEnum.Sales)
      if (response?.statusCode === 200 || response?.status === 200) {
        const checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint
        setCheckpoints(checkpointsArr)
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
      setCpLoading(false)
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
      setCpLoading(false)
    }
  }

  const getPositionMembers = async (positionId: string) => {
    try {
      // const response = await getPositionMembersById({ positionId }, false)  // NHR-1570
      const response = await getSalesPersonAndPM() // NHR-1570
      if (isSuccess(response)) {
        // setSalesPersonDrop(response?.data?.data?.memberData) // NHR-1570
        setSalesPersonDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }
  const getPositionMembersForSalesManager = async (positionId: string) => {
    try {
      // const response = await getPositionMembersById({ positionId }, false)  // NHR-1570
      const response = await getSalesPersonAndPM(undefined, true) // NHR-1570
      if (isSuccess(response)) {
        // setSalesManagerDrop(response?.data?.data?.memberData) // NHR-1570
        setSalesManagerDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }
  const getPositions = async () => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: I_Position[] = response?.data?.data?.position
        let salesPersonIdx: string[] = []
        let salesManagerIdx = 0
        positions.forEach((position: any, idx) => {
          if (position.symbol === 'SalesPerson') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'GeneralManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'SalesManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'RRTech') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'ProjectManager') {
            salesManagerIdx = idx
          }
        })
        getPositionMembers(salesPersonIdx?.join())
        // getPositionMembers(positions[salesPersonIdx]._id)
        getPositionMembersForSalesManager(positions[salesManagerIdx]._id)
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false, [StageGroupEnum.Sales, StageGroupEnum.Operations]?.join())
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const initFetchOpportunity = async () => {
    try {
      const response = await getOpportunityById({
        deleted: deletedFlag,
        opportunityId: oppId!,
      })
      if (isSuccess(response)) {
        const opp: I_Opportunity = response?.data?.data?.opportunity
        setOppData(opp)
        setDistance(opp?.distance || 0)
        setInputValue(`${opp?.PO || ''}-${opp?.num || ''}`)
        setDuration(opp?.duration || 0)
        fetchCheckpoints()
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('INIT FETCH ERR', err)
    }
  }

  const initFetch = async (showLoader = true) => {
    showLoader && setLoading(true)
    try {
      const response = await getOpportunityById({
        deleted: deletedFlag,
        opportunityId: oppId!,
      })
      if (isSuccess(response)) {
        const opp: I_Opportunity = response?.data?.data?.opportunity

        setIsLost(opp.status === 'lost')
        setOppData(opp)
        setInputValue(`${opp?.PO || ''}-${opp?.num || ''}`)
        setDuration(opp?.duration)
        showLoader && !isRestrictedOpp && (await getContact(opp.contactId))
        !isRestrictedOpp && fetchActivity()
        showLoader && !isRestrictedOpp && getLeadSrcData()
        showLoader && !isRestrictedOpp && getPositions()
        showLoader && !isRestrictedOpp && fetchProject()
        showLoader && !isRestrictedOpp && getStagesData()
        showLoader && !isRestrictedOpp && fetchCheckpoints()
        // showLoader && fetchSalesActions()
        showLoader && setLoading(false)
      } else {
        if (response?.data?.message === 'This opportunity does not belong to you') {
          return navigate(`/sales/${userIdLocal}`)
        } else {
          throw new Error(response?.data?.message)
        }
      }
    } catch (err) {
      console.log('INIT FETCH ERR', err)
      showLoader && setLoading(false)
    }
  }

  const getCurStageId = () => {
    let curStageId = -1
    stages!.forEach((stage, idx) => {
      if (stage.name === oppData!.stage) {
        curStageId = idx
        return
      }
    })
    return curStageId
  }

  const handleDeleteOpportunity = async () => {
    if (!!projectTableValues?.length) {
      notify('Can not delete Opportunity!!, Contains active Projects!', 'error')
      return
    }
    setBtnLoading(true)
    try {
      const res = await deleteOpportunity({ id: oppData?._id })
      if (isSuccess(res)) {
        setBtnLoading(false)
        notify('Opportunity deleted successfully!', 'success')
        navigate(`/${operationsFlag ? 'operations' : 'sales'}`)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      setBtnLoading(false)
      notify('Failed to Delete opportunity!', 'error')
      console.log('Submit error', err)
    }
  }
  const createPO = (name: string, address: string) => {
    // Process name
    name = name
      ?.replace(/[\W_]+/g, '')
      ?.slice(0, 4)
      ?.toUpperCase()

    // Process address
    address = address ? address.split(' ')[0] : ''
    address = address.padStart(4, '0').slice(0, 4)
    // Generate PO
    const po = name + address
    return po
  }

  const createPOWithNum = async (name: string, address: string) => {
    // Process name
    name = name
      ?.replace(/[\W_]+/g, '')
      ?.slice(0, 4)
      ?.toUpperCase()

    // Process address
    address = address ? address.split(' ')[0] : ''
    address = address.padStart(4, '0').slice(0, 4)
    // Generate PO
    const po = name + address
    const res = await getPONum(po, oppData?.num?.includes('W') ? true : false)
    if (isSuccess(res)) {
      const poNum = { po: po || oppData.PO, num: res?.data || oppData?.num }
      setPoNum(poNum)
      return poNum
    }
    return { po: oppData.PO, num: oppData.num }
  }
  // getSalesPersonIdFromName

  const handleClientSubmit = async (values: typeof initClientData) => {
    // const type = getIdFromName(values.type, projectTypesDrop)
    const leadSourceId = getIdFromName(values?.leadSource, leadSrcDrop)
    {
      /* hardcoded code */
    }
    const referrerId =
      leadSourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
        ? getStageIdFromName(values.referredBy, refererres)
        : undefined

    const type = getIdFromName(values.type, projectTypesDrop)
    if (oppData && currentCompany) {
      setClientLoading(true)
      try {
        const { __v, _id, salesPerson, stage, ...restData } = oppData
        let salesPersonId = '',
          projectManagerId = '',
          stageId = ''
        restData.oppType = type
        // let PO = oppData?.PO
        let confirmed
        if ((oppData?.contact?.zip === '' || !oppData?.contact?.zip) && values.oppCity !== oppData?.oppCity) {
          confirmed = window.confirm('Would you like to save this address to the Contact File as well?')
        }

        if (confirmed) {
          let dataObj: any = {
            businessName: oppData?.contact?.businessName || '',
            firstName: oppData?.contact?.firstName || '',
            lastName: oppData?.contact?.lastName || '',
            street: values.street || '',
            city: values.oppCity || '',
            state: values.oppState || '',
            zip: values.zip || '',
            phone: oppData?.contact?.phone || '',
            email: oppData?.contact?.email || '',
            leadSourceName: oppData?.contact?.leadSourceName || '',
            leadSource: getIdFromName(oppData?.contact?.leadSourceName, leadSrcDrop),
            referredBy: referrerId,
            createdBy: currentMember._id,
            // contactId: oppData?.contact?._id,
            // cotactId: oppData?.contact?._id,
            distance: Number(distance),
            duration: Number(duration),
          }
          let response = await updateContact(dataObj, oppData?.contact?._id)
          if (response?.data?.statusCode === 200) {
            notify('Contact Profile Edited Successfully', 'success')
            setLoading(false)
          } else {
            setLoading(false)
          }
        }

        // if (oppData?.street === '') {
        //   const name =
        //     oppData?.client?.lastName !== '' ? oppData?.client?.lastName : oppData?.client?.firstName
        //   PO = createPO(name, values?.street)
        // }

        // ------------------my edit--------------------
        // restData.leadDate = values.dateReceived.includes('"')
        //   ? startOfDate(values.dateReceived)
        //   : startOfDate(`"${values.dateReceived}"`)
        restData.leadDate = values.dateReceived

        salesPersonId = getSalesPersonIdFromName(values.assignedTo, salesPersonDrop) || oppData?.salesPerson
        if (projectManagerName !== '')
          projectManagerId = getSalesPersonIdFromName(projectManagerName, salesManagerDrop) || oppData?.projectManager
        // projectManagerId = getSalesPersonIdFromName(values.projectManager, salesManagerDrop)

        if (taxUniqueState.some((state: any) => values.oppState.includes(state))) {
          values.taxJurisdiction = getTaxJurisdictionIdFromName(values.taxJurisdiction, formatedTax)
        } else {
          values.taxJurisdiction = ''
        }
        const workingCrew = crewSub?.find((v: any) => v.name === values.workingCrew)
        // if (!values.addrCityState.includes(' WA')) {
        //   values.taxJurisdiction = ''
        // } else {
        //   values.taxJurisdiction = getTaxJurisdictionIdFromName(values.taxJurisdiction, tax)
        // }
        // ------------------my edit--------------------
        // salesPersonDrop.forEach((sp) => {
        //   if (sp._id === salesPerson) salesPersonId = sp._id
        // })

        values.distance = Number(values.distance)
        stageId = getStageIdFromName(values.stage, stages)
        const matchedStageObject = stages.find((v) => v._id === stageId)

        const response = await updateOpportunity({
          ...values,
          leadSourceId: leadSourceId != '' ? leadSourceId : undefined,
          PO: oppData?.PO,
          num: oppData?.num,
          newLeadDate: restData?.leadDate,
          city: values.oppCity,
          state: values.oppState,
          referredBy: referrerId,
          createdBy: restData?.createdBy,
          oppType: restData?.oppType,
          contactId: restData?.contactId,
          firstName: restData?.firstName,
          lastName: restData?.lastName,
          opportunityId: oppData._id,
          editedBy: currentMember._id!,
          currDate: new Date().toISOString(),
          salesPerson: salesPersonId,
          workingCrew: workingCrew,
          projectManager: projectManagerId === '' ? null : projectManagerId,
          stage: stageId,
          duration: Number(values?.duration),
        })
        if (isSuccess(response)) {
          navigate(`/${getEnumValue(matchedStageObject.stageGroup)}/opportunity/${oppId}`)
          notify('Opportunity Updated!', 'success')
          setClientLoading(false)
          initFetchOpportunity()
          getContact(oppData.contactId)
          fetchActivity()
        } else {
          notify(simplifyBackendError(response?.data?.message), 'error')
          setClientLoading(false)
        }
      } catch (err) {
        setClientLoading(false)

        console.log(err)
      }
    }
  }

  const handleClick = () => {
    setEditing(true)
  }

  const handleInputBlurValue = async (
    data: any,
    activityString: string,
    resetCallback?: () => void,
    isAddress?: boolean
  ) => {
    const hasValues = Object.values(data)?.filter(Boolean)

    if (!hasValues?.length) {
      const keys = Object.keys(data)
      notify(`Please enter ${keys[0]}`, 'error')
      return
    }

    try {
      const res = await updateOpportunity({
        opportunityId: oppData?._id!,
        editedBy: currentMember._id!,
        currDate: new Date().toISOString(),
        ...data,
      })
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        setPoNum({ po: '', num: '' })
        setActivityLoading(true)
        await updateActivity({
          id: oppId!,
          memberId: currentMember._id!,
          body: activityString,
          currDate: new Date().toISOString(),
        })

        if (isAddress) {
          if (hasValueChanged(oppData?.PO!, data?.PO) || hasValueChanged(oppData?.num!, data?.num)) {
            await updateActivity({
              id: oppId!,
              memberId: currentMember._id!,
              body: `changed PO# from ${oppData?.PO || ''}-${oppData?.num || ''} to ${data?.PO || ''}-${data?.num}`,
              currDate: new Date().toISOString(),
            })
          }
        }

        initFetch(false)
      } else {
        setInputValue(`${oppData?.PO || ''}-${oppData?.num || ''}`)
      }
    } catch (error) {
      console.error('Update-opp error=====>', error)
    } finally {
      resetCallback?.()
    }
  }

  const handleBlur = () => {
    console.log(extractPoInfo(inputValue, 'po'), extractPoInfo(inputValue, 'num'), 'tttt', inputValue)
    setOppData((prev: any) => ({
      ...prev,
      PO: extractPoInfo(inputValue, 'po'),
      num: extractPoInfo(inputValue, 'num'),
    }))
    setEditing(false)
  }

  const handleRestoreClick = async () => {
    try {
      setRestoreLoading(true)
      const res = await restoreOpportunity(oppId!)
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        navigate(`/sales`)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Restore error', err)
    } finally {
      setRestoreLoading(false)
    }
  }

  const fetchLinkedContact = async () => {
    setLinkedContactLoading(true)
    try {
      const res = await getLinkedContact(oppData?.contactId!)
      if (isSuccess(res)) {
        setLinkedContactData(res?.data?.data?.linkedContacts)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setLinkedContactLoading(false)
    }
  }

  useEffect(() => {
    if (oppData?.contactId) {
      fetchLinkedContact()
    }
  }, [oppData?.contactId])

  const viewJobReportArray = ['Current Job', 'Job Finalization', 'Completed']
  const disableForTax = !companySettingForAll?.workingStates?.includes(oppData?.state)
    ? true
    : (oppData?.taxJurisdiction === '' || !oppData?.taxJurisdiction) &&
      taxUniqueState?.some((state: any) => oppData?.state?.includes(state))
    ? true
    : false
  return loading ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
    </>
  ) : oppData ? (
    <LoadScript
      googleMapsApiKey={getConfig().googleAddressApiKey}
      //  @ts-ignore
      libraries={['places']}
      loadingElement={<SLoader height={35} width={100} isPercent />}
    >
      <Styled.OppContainer
        navCollapsed={navCollapsed}
        className={deletedFlag ? 'is-opp-deleted' : isRestrictedOpp ? 'subcontractor' : ''}
      >
        {/* <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
        <SharedStyled.SectionTitle>
          {' '}
          PO#: {oppData.PO}-{oppData.num}
        </SharedStyled.SectionTitle>
      </SharedStyled.FlexRow> */}

        {deletedFlag ? (
          <Styled.DeletedContent className="deleted-opp">
            <SharedStyled.FlexCol alignItems="center" gap="20px" justifyContent="flex-start" margin="40px 0 0 0">
              <p>This Opportunity has been deleted</p>
              <Button width="max-content" onClick={handleRestoreClick} isLoading={restoreLoading}>
                RestoresortInventoryMats
              </Button>
            </SharedStyled.FlexCol>
          </Styled.DeletedContent>
        ) : null}

        <SharedStyled.FlexRow
          width="100%"
          gap="24px"
          alignItems="flex-start"
          flexWrap={width < 1024 ? 'wrap' : 'nowrap'}
        >
          <SharedStyled.FlexCol gap="34px">
            <Styled.PoContainer wrapperPadding="24px" className={isOppInfoPermissionReadOnly ? 'read-only' : ''}>
              <Formik
                initialValues={initClientData}
                onSubmit={handleClientSubmit}
                validateOnChange={true}
                validateOnBlur={false}
                enableReinitialize
              >
                {({ errors, touched, values, setFieldValue, handleSubmit, resetForm }) => {
                  const code = getValueByKeyAndMatch('code', values?.leadSource, 'name', leadSrcDrop)

                  useEffect(() => {
                    setFieldValue('street', values.street)
                    setFieldValue('oppCity', values.oppCity)
                    setFieldValue('oppState', values.oppState)
                    setFieldValue('zip', values.zip)
                  }, [distance, values.street, values.oppCity, values.oppState, values.zip, duration])
                  const handleCheckDistance = () => {
                    if (values.tempDistance === 0) {
                      notify('Unable to fetch distance enter it manually', 'error')
                      setShowDriveTimeModal(true)
                      return true
                    }
                    if (addressInputType === 'custom') {
                      setShowDriveTimeModal(true)
                      return true
                    }
                  }

                  const checkDistanceAndUpdateAddress = async (isUpdateOnlyAddress?: boolean) => {
                    const name = oppData?.contact?.fullName
                    const resp = handleCheckDistance()
                    if (resp) return

                    let oppLat: string = ''
                    let oppLong: string = ''

                    if (values?.street && values?.oppCity && values?.zip && values?.oppState) {
                      const { lat, lng } = await getLatLongFromAddress(
                        `${values?.street || ''}, ${values?.oppCity || ''}, ${values?.oppState || ''} ${
                          values?.zip || ''
                        }, USA`
                      )

                      oppLat = lat || ''
                      oppLong = lng || ''
                    }
                    console.log({ oppLat, oppLong })

                    await handleInputBlurValue(
                      {
                        street: values?.street,
                        city: values?.oppCity,
                        state: values?.oppState,
                        zip: values?.zip,
                        PO: isUpdateOnlyAddress ? oppData?.PO : createPO(name, values?.street),
                        duration,
                        distance,
                        oppLat: `${oppLat}` || undefined,
                        oppLong: `${oppLong}` || undefined,
                        num: isUpdateOnlyAddress ? oppData?.num : '01',
                      },
                      `updated Address from ${oppData?.street}, ${oppData?.city}, ${oppData?.state}, ${oppData?.zip} to ${values?.street}, ${values?.oppCity}, ${values?.oppState}, ${values?.zip}`,
                      () => {},
                      isUpdateOnlyAddress ? false : true
                    )
                    setToggleAddress(false)
                    setAddressInputType('google')
                  }

                  return clientLoading ? (
                    <SharedStyled.CardSkeleton height="500px"></SharedStyled.CardSkeleton>
                  ) : (
                    <>
                      <div className="data-wrapper custom-styles">
                        {isInactive && (
                          <div className="state-inactive">
                            <SharedStyled.Text fontSize="12px">This Opportunity is Inactive</SharedStyled.Text>
                          </div>
                        )}
                        {isLost && (
                          <div className="state-error">
                            <SharedStyled.Text fontSize="12px">This Opportunity is Lost</SharedStyled.Text>
                          </div>
                        )}

                        <SharedStyled.FlexRow alignItems="flex-start">
                          <SharedStyled.ContentHeader pointer="pointer" textAlign="left" as="h3" onClick={handleClick}>
                            PO#:{' '}
                            {editing ? (
                              <input
                                type="text"
                                className="po-input"
                                value={inputValue}
                                onChange={(e: any) => setInputValue(e.target.value)}
                                onBlur={async () => {
                                  // handleBlur()
                                  if (
                                    hasValueChanged(oppData?.PO, extractPoInfo(inputValue, 'po')) ||
                                    hasValueChanged(oppData?.num, extractPoInfo(inputValue, 'num'))
                                  ) {
                                    const confirmed = window.confirm(
                                      `PO# changed from ${oppData?.PO}-${oppData?.num} to ${
                                        extractPoInfo(inputValue, 'po') || ''
                                      }-${extractPoInfo(inputValue, 'num') || ''}, accept change?`
                                    )
                                    if (confirmed) {
                                      handleInputBlurValue(
                                        {
                                          PO: extractPoInfo(inputValue, 'po'),
                                          num: extractPoInfo(inputValue, 'num'),
                                          warrantyType: oppData?.warrantyType ? oppData.warrantyType : false,
                                        },
                                        `changed PO# from ${oppData?.PO || ''}-${oppData?.num || ''} to ${
                                          extractPoInfo(inputValue, 'po') || ''
                                        }-${oppData?.num}`
                                      )
                                    } else {
                                      setInputValue(`${oppData?.PO}-${oppData?.num}`)
                                    }
                                  }
                                }}
                                autoFocus
                              />
                            ) : (
                              <>
                                {oppData.PO}-{oppData.num}
                              </>
                            )}
                          </SharedStyled.ContentHeader>
                          {positionPermissions?.media ? (
                            <div>
                              <Link
                                to={
                                  operationsFlag
                                    ? `/operations/opportunity/${oppId}/media`
                                    : `/sales/opportunity/${oppId}/media`
                                }
                                state={{
                                  poName: `${oppData.PO}-${oppData.num}`,
                                  clientName: oppData?.contact?.fullName,
                                }}
                              >
                                <img
                                  style={{
                                    maxWidth: '100%',
                                    width: '40px',
                                    aspectRatio: 1,
                                    cursor: 'pointer',
                                  }}
                                  src={ImageIcon}
                                  alt="media icon"
                                />
                              </Link>
                            </div>
                          ) : null}

                          {positionPermissions?.forms ? (
                            <FormSelect oppId={oppId!} data={{ whereIsUse: FormAccess.Opportunity }} />
                          ) : null}
                        </SharedStyled.FlexRow>

                        <SharedStyled.FlexRow
                          alignItems="center"
                          width="100%"
                          gap="12px"
                          flexDirection="row-reverse"
                          justifyContent="space-between"
                        >
                          {' '}
                          <p>{oppData?.contact?.isBusiness ? 'Business Name' : 'Name'}</p>
                          <span
                            className="bold"
                            style={{ color: colors.darkBlue, cursor: isRestrictedOpp ? 'default' : 'pointer' }}
                            onClick={() => {
                              !isRestrictedOpp && navigate(`/contact/profile/${oppData?.contact?._id}/${false}`)
                            }}
                          >
                            {/* {oppData?.client?.fullName} */}
                            {oppData?.contact?.isBusiness ? oppData?.contact?.businessName : oppData?.contact?.fullName}
                          </span>
                        </SharedStyled.FlexRow>

                        {oppData?.contact?.isBusiness && (
                          <SharedStyled.FlexRow
                            alignItems="center"
                            width="100%"
                            gap="12px"
                            flexDirection="row-reverse"
                            justifyContent="space-between"
                          >
                            {' '}
                            <p>{'Primary Contact'}</p>
                            <span className="bold">
                              {/* {oppData?.client?.fullName} */}
                              {oppData?.contact?.fullName}
                            </span>
                          </SharedStyled.FlexRow>
                        )}

                        <SharedStyled.FlexRow
                          alignItems="center"
                          width="100%"
                          gap="12px"
                          justifyContent="space-between"
                          className="subcontractor-hide"
                        >
                          <SharedStyled.Text color={colors.darkGrey}>
                            {
                              oppData?.contact?.phone ? formatPhoneNumber(oppData?.contact?.phone, '') : '--'
                              // '--' + ' | ' + oppData?.contact?.cPhone2
                              // ? formatPhoneNumber(oppData?.contact?.cPhone2, '')
                              // : '--'
                            }
                          </SharedStyled.Text>

                          <SharedStyled.Text
                            style={{ textTransform: 'unset' }}
                            textTransform="none"
                            color={colors.darkGrey}
                          >
                            {oppData?.contact?.email || '--'}
                          </SharedStyled.Text>
                        </SharedStyled.FlexRow>

                        <>
                          {linkedContactData?.length > 0 &&
                            linkedContactData?.map((contact: any, index: number) => {
                              return (
                                <>
                                  <SharedStyled.FlexRow
                                    alignItems="center"
                                    width="100%"
                                    gap="12px"
                                    flexDirection="row-reverse"
                                    justifyContent="space-between"
                                  >
                                    {' '}
                                    <p>Contact #{index + 1}</p>
                                    <span
                                      className="bold"
                                      onClick={() => {
                                        !isRestrictedOpp && navigate(`/contact/profile/${contact?.id?._id}/${false}`)
                                      }}
                                      style={{ color: colors.darkBlue, cursor: 'pointer' }}
                                    >
                                      {contact?.id?.fullName || '--'}
                                    </span>
                                  </SharedStyled.FlexRow>
                                  <SharedStyled.FlexRow
                                    alignItems="center"
                                    width="100%"
                                    gap="12px"
                                    justifyContent="space-between"
                                  >
                                    <SharedStyled.Text color={colors.darkGrey} margin="0 0 0 10px">
                                      {formatPhoneNumber(contact?.id?.phone, '') || '--'}
                                    </SharedStyled.Text>

                                    <SharedStyled.Text style={{ textTransform: 'unset' }} color={colors.darkGrey}>
                                      {contact?.id?.email || '--'}
                                    </SharedStyled.Text>
                                  </SharedStyled.FlexRow>
                                </>
                              )
                            })}
                        </>

                        <SharedStyled.FlexBox width="100%" alignItems="flex-start" gap="12px" className="google">
                          <SharedStyled.FlexCol alignItems="flex-end" gap="4px">
                            <p>Address</p>
                            {!toggleAddress && addressInputType !== 'custom' && (
                              <SharedStyled.TooltipContainer
                                width={'160px'}
                                // positionLeft="50%"
                                // positionBottom="0px"
                                positionLeftDecs={'-100%'}
                                positionBottomDecs="100%"
                                className="no-pointer"
                              >
                                <span
                                  style={{ color: colors.darkBlue, fontFamily: Nue.medium }}
                                  className="link edit-address"
                                  onClick={() => {
                                    setToggleAddress(true)
                                    setAddressInputType('google')
                                  }}
                                >
                                  Edit
                                </span>
                              </SharedStyled.TooltipContainer>
                            )}
                          </SharedStyled.FlexCol>

                          {!toggleAddress ? (
                            // custom address input

                            <Styled.AddressCont gap="12px" id="custom">
                              <SharedStyled.FlexCol gap="4px">
                                {/* <SharedStyled.FlexBox alignItems="center" justifyContent="center" className="street"> */}
                                {/* <p>Address:</p> */}

                                <SharedStyled.FlexRow className="input">
                                  <div title="Street" id="street">
                                    {addressInputType === 'custom' ? (
                                      <InputWithValidation
                                        labelName="Street"
                                        stateName="street"
                                        error={touched.street && errors.street ? true : false}
                                        twoInput={true}
                                        // onBlur={() => {
                                        //   if (hasValueChanged(oppData?.street, values?.street))
                                        //     handleInputBlurValue(
                                        //       {
                                        //         street: values.street,
                                        //       },
                                        //       `changed Street from ${oppData?.street} to ${values?.street}`,
                                        //       () => {}
                                        //     )
                                        // }}
                                      />
                                    ) : (
                                      <>{values?.street}</>
                                    )}
                                  </div>
                                </SharedStyled.FlexRow>
                                {/* </SharedStyled.FlexBox> */}

                                {/* <SharedStyled.FlexRow> */}
                                {/* <CustomSelect
                              labelName="City / State"
                              stateName="addrCityState"
                              error={touched.addrCityState && errors.addrCityState ? true : false}
                              setFieldValue={setFieldValue}
                              value={values.addrCityState!}
                              dropDownData={cityDropdown}
                              setValue={() => {}}
                              innerHeight="52px"
                              margin="0"
                              isSmall
                            /> */}
                                {/* <SharedStyled.FlexBox alignItems="center" justifyContent="center" className="oppCity">
                              <p>City:</p>
                              <InputWithValidation
                                labelName="City"
                                stateName="oppCity"
                                error={touched.oppCity && errors.oppCity ? true : false}
                                twoInput={true}
                                isSmall
                              />
                            </SharedStyled.FlexBox> */}
                                {/* <SharedStyled.FlexBox alignItems="center" justifyContent="center" className="oppState"> */}
                                {/* <p>State:</p> */}

                                <SharedStyled.FlexRow className="input" justifyContent="space-between">
                                  {addressInputType === 'custom' ? (
                                    <div title="City" id="city">
                                      <InputWithValidation
                                        labelName="City"
                                        stateName="oppCity"
                                        error={touched.oppCity && errors.oppCity ? true : false}
                                        twoInput={true}
                                        // onBlur={() => {
                                        //   if (hasValueChanged(oppData?.city, values?.oppCity))
                                        //     handleInputBlurValue(
                                        //       {
                                        //         city: values.oppCity,
                                        //       },
                                        //       `changed City from ${oppData?.city} to ${values?.oppCity}`,
                                        //       () => {}
                                        //     )
                                        // }}
                                      />
                                    </div>
                                  ) : (
                                    <>{values?.oppCity}</>
                                  )}

                                  {addressInputType === 'custom' ? (
                                    <div title="State" id="state">
                                      <CustomSelect
                                        dropDownData={companySettingForAll?.workingStates || []}
                                        setValue={() => {}}
                                        stateName="oppState"
                                        value={values.oppState}
                                        // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                                        setFieldValue={setFieldValue}
                                        labelName="State"
                                        innerHeight="52px"
                                        margin="0 10px 0 0"
                                        // onBlur={() => {
                                        //   if (hasValueChanged(oppData?.state, values?.oppState))
                                        //     handleInputBlurValue(
                                        //       {
                                        //         state: values.oppState,
                                        //       },
                                        //       `changed State from ${oppData?.state} to ${values?.oppState}`,
                                        //       () => {}
                                        //     )
                                        // }}
                                      />
                                    </div>
                                  ) : (
                                    <>,&nbsp;&nbsp;{values?.oppState}</>
                                  )}
                                  {/* <InputWithValidation
                                      labelName="State"
                                      stateName="oppState"
                                      error={touched.oppState && errors.oppState ? true : false}
                                      twoInput={true}
                                      isSmall
                                    /> */}

                                  {addressInputType === 'custom' ? (
                                    <div title="Zip" className="zip">
                                      <InputWithValidation
                                        labelName="Zip"
                                        stateName="zip"
                                        error={touched.zip && errors.zip ? true : false}
                                        twoInput={true}
                                        // onBlur={() => {
                                        //   if (hasValueChanged(oppData?.zip, values?.zip))
                                        //     handleInputBlurValue(
                                        //       {
                                        //         zip: values.zip,
                                        //       },
                                        //       `changed Zip from ${oppData?.zip} to ${values?.zip}`,
                                        //       () => {}
                                        //     )
                                        // }}
                                      />
                                    </div>
                                  ) : (
                                    <>&nbsp;&nbsp;{values?.zip}</>
                                  )}
                                </SharedStyled.FlexRow>
                                {/* </SharedStyled.FlexBox> */}
                                {/* <SharedStyled.FlexBox alignItems="center" justifyContent="center" className="zip">
                              <p>Zip:</p>
                              <InputWithValidation
                                labelName="Zip"
                                stateName="zip"
                                error={touched.zip && errors.zip ? true : false}
                                twoInput={true}
                                isSmall
                              />
                            </SharedStyled.FlexBox> */}
                                {/* </SharedStyled.FlexRow> */}
                                {addressInputType === 'custom' ? (
                                  <SharedStyled.FlexRow
                                    style={{ width: '100%' }}
                                    margin="4px 0 0 0"
                                    flexWrap="wrap"
                                    justifyContent="space-between"
                                  >
                                    <SharedStyled.FlexRow width="max-content">
                                      <Button
                                        type="button"
                                        width="max-content"
                                        onClick={async () => {
                                          setFieldValue('duration', duration)
                                          setFieldValue('distance', values?.tempDistance)

                                          const name =
                                            oppData?.contact?.lastName && oppData?.contact?.lastName !== ''
                                              ? oppData?.contact?.lastName
                                              : oppData?.contact?.firstName

                                          if (values?.street !== oppData?.street) {
                                            if (hasValueChanged(oppData?.PO, createPO(name, values?.street))) {
                                              const res = await createPOWithNum(name, values?.street)
                                              const confirmed = window.confirm(
                                                `PO# changed from ${oppData?.PO}-${oppData?.num} to ${res.po || ''}-${
                                                  res.num || ''
                                                }, accept change?`
                                              )
                                              // const confirmed = window.confirm(
                                              //   `PO# changed from ${oppData?.PO}-${oppData?.num} to ${
                                              //     createPO(name, values?.street) || ''
                                              //   }-${oppData?.num || ''}, accept change?`
                                              // )
                                              if (confirmed) {
                                                checkDistanceAndUpdateAddress()
                                              } else {
                                                setPoNum({ po: '', num: '' })
                                                checkDistanceAndUpdateAddress(true)
                                                setToggleAddress(false)
                                                setAddressInputType('google')
                                                // resetForm()
                                                setDistance(oppData?.distance)
                                              }
                                            } else {
                                              checkDistanceAndUpdateAddress()
                                            }
                                          } else {
                                            checkDistanceAndUpdateAddress()
                                          }
                                        }}
                                      >
                                        Save
                                      </Button>
                                      <Button
                                        className="delete"
                                        type="button"
                                        width="max-content"
                                        onClick={() => {
                                          setToggleAddress(false)
                                          setAddressInputType('google')
                                          resetForm()
                                          setDistance(oppData?.distance)
                                        }}
                                      >
                                        Cancel
                                      </Button>
                                    </SharedStyled.FlexRow>
                                    <Button
                                      type="button"
                                      onClick={() => {
                                        setAddressInputType('google')
                                        setToggleAddress(true)
                                      }}
                                      className="gray"
                                      width="max-content"
                                    >
                                      Google
                                    </Button>
                                  </SharedStyled.FlexRow>
                                ) : null}
                              </SharedStyled.FlexCol>
                              {/* <span
                              style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                              className="link"
                              onClick={() => setToggleAddress(!toggleAddress)}
                            >
                              Edit
                            </span> */}
                            </Styled.AddressCont>
                          ) : (
                            <>
                              <Styled.AddressCont gap="12px" className="google" id="google">
                                <SharedStyled.FlexCol width="100%">
                                  <AutoCompleteAddress
                                    setFieldValue={setFieldValue}
                                    street={'street'}
                                    city={'oppCity'}
                                    state={'oppState'}
                                    zip={'zip'}
                                    initialText={`${values?.street} ${values?.oppCity} ${values?.oppState} ${values?.zip}`}
                                    sourceAddress={companySettingForAll?.address}
                                    companyLatLong={companySettingForAll}
                                    setDistance={setDistance}
                                    distance="tempDistance"
                                    height="35px"
                                    fontSize="8px"
                                    duration=""
                                    setDuration={setDuration}
                                    noLoadScript={true}
                                  />

                                  <SharedStyled.FlexRow
                                    style={{ width: '100%' }}
                                    margin="4px 0 0 0"
                                    flexWrap="wrap"
                                    justifyContent="space-between"
                                  >
                                    <SharedStyled.FlexRow width="max-content">
                                      <Button
                                        type="button"
                                        width="max-content"
                                        onClick={async () => {
                                          setFieldValue('duration', duration)
                                          setFieldValue('distance', values?.tempDistance)

                                          const name =
                                            oppData?.contact?.lastName && oppData?.contact?.lastName !== ''
                                              ? oppData?.contact?.lastName
                                              : oppData?.contact?.firstName

                                          if (values?.street !== oppData?.street) {
                                            if (values?.street !== oppData?.street) {
                                              if (hasValueChanged(oppData?.PO, createPO(name, values?.street))) {
                                                const res = await createPOWithNum(name, values?.street)
                                                const confirmed = window.confirm(
                                                  `PO# changed from ${oppData?.PO}-${oppData?.num} to ${res.po || ''}-${
                                                    res.num || ''
                                                  }, accept change?`
                                                )
                                                if (confirmed) {
                                                  checkDistanceAndUpdateAddress()
                                                } else {
                                                  setPoNum({ po: '', num: '' })
                                                  checkDistanceAndUpdateAddress(true)
                                                  // setToggleAddress(false)
                                                  setAddressInputType('google')
                                                  // resetForm()
                                                  setDistance(oppData?.distance)
                                                }
                                              } else {
                                                checkDistanceAndUpdateAddress()
                                              }
                                            } else {
                                              checkDistanceAndUpdateAddress()
                                            }
                                          } else {
                                            checkDistanceAndUpdateAddress()
                                          }
                                        }}
                                      >
                                        Save
                                      </Button>
                                      <Button
                                        className="delete"
                                        onClick={() => {
                                          setToggleAddress(false)
                                          resetForm()
                                          setDistance(oppData?.distance)
                                        }}
                                        type="button"
                                        width="max-content"
                                      >
                                        Cancel
                                      </Button>
                                    </SharedStyled.FlexRow>
                                    <Button
                                      type="button"
                                      onClick={() => {
                                        setAddressInputType('custom')
                                        setToggleAddress(false)
                                      }}
                                      className="gray"
                                      width="max-content"
                                    >
                                      Custom
                                    </Button>
                                  </SharedStyled.FlexRow>
                                  {/* <span
                                  style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                                  className="link"
                                  onClick={() => {
                                    setToggleAddress(!toggleAddress)
                                    handleCheckDistance()
                                    setAddressObj({
                                      street: values?.street,
                                      city: values?.oppCity,
                                      state: values?.oppState,
                                      zip: values?.zip,
                                    })
                                  }}
                                >
                                  close
                                </span> */}
                                </SharedStyled.FlexCol>
                              </Styled.AddressCont>
                            </>
                          )}
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox alignItems="center" gap="12px" className="subcontractor-hide">
                          <p>Lead Date</p>
                          <SharedDateAndTime
                            isSmall
                            value={values.dateReceived}
                            labelName="Lead Date"
                            stateName="dateReceived"
                            disableFocus
                            onBlur={() => {
                              if (hasValueChanged(formatDateymd(oppData?.newLeadDate), values?.dateReceived))
                                handleInputBlurValue(
                                  {
                                    newLeadDate: new Date(values?.dateReceived),
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                  },
                                  `changed Lead Date from ${dayjsFormat(
                                    oppData?.dateReceived,
                                    'MM/DD/YYYY HH:mm a'
                                  )} to ${dayjsFormat(values?.dateReceived, 'MM/DD/YYYY HH:mm a')}`
                                )
                            }}
                          />
                          {/* <SharedDate
                          isSmall
                          value={values.dateReceived}
                          labelName="Lead Date"
                          stateName="dateReceived"
                          disableFocus
                          onBlur={() => {
                            if (hasValueChanged(formatDateymd(oppData?.newLeadDate), values?.dateReceived))
                              handleInputBlurValue(
                                {
                                  newLeadDate: startOfDate(values?.dateReceived),
                                  PO: oppData?.PO,
                                  num: oppData?.num,
                                },
                                `changed Date Received from ${dayjsFormat(
                                  oppData?.dateReceived,
                                  'MM/DD/YYYY'
                                )} to ${dayjsFormat(values?.dateReceived, 'MM/DD/YYYY')}`
                              )
                          }}
                        /> */}
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox alignItems="center" gap="12px" className="subcontractor-hide">
                          <p>Opportunity Date</p>
                          <SharedDateAndTime
                            isSmall
                            value={values.oppDateReceived}
                            labelName="Opportunity Date"
                            stateName="oppDateReceived"
                            disableFocus
                            onBlur={() => {
                              if (hasValueChanged(formatDateymd(oppData?.newLeadDate), values?.oppDateReceived))
                                handleInputBlurValue(
                                  {
                                    oppDate: new Date(values?.oppDateReceived),
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                  },
                                  `changed Opportunity Date from ${dayjsFormat(
                                    oppData?.oppDateReceived,
                                    'MM/DD/YYYY HH:mm a'
                                  )} to ${dayjsFormat(values?.oppDateReceived, 'MM/DD/YYYY HH:mm a')}`
                                )
                            }}
                          />
                          {/* <SharedDate
                          isSmall
                          value={values.oppDateReceived}
                          labelName="Date received"
                          stateName="oppDateReceived"
                          disableFocus
                          onBlur={() => {
                            if (hasValueChanged(formatDateymd(oppData?.newLeadDate), values?.oppDateReceived))
                              handleInputBlurValue(
                                {
                                  oppDate: startOfDate(values?.oppDateReceived),
                                  PO: oppData?.PO,
                                  num: oppData?.num,
                                },
                                `changed Date Received from ${dayjsFormat(
                                  oppData?.oppDateReceived,
                                  'MM/DD/YYYY'
                                )} to ${dayjsFormat(values?.oppDateReceived, 'MM/DD/YYYY')}`
                              )
                          }}
                        /> */}
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox width="100%" alignItems="center" gap="12px">
                          <p>Distance (mi) </p>
                          <div className="distance">
                            <InputWithValidation
                              labelName="Distance"
                              stateName="distance"
                              error={touched.distance && errors.distance ? true : false}
                              twoInput={true}
                              isSmall
                              onBlur={() => {
                                if (hasValueChanged(oppData?.distance, Number(values?.distance)))
                                  handleInputBlurValue(
                                    {
                                      distance: Number(values?.distance),
                                      PO: oppData?.PO,
                                      num: oppData?.num,
                                    },
                                    `changed Distance from ${oppData?.distance || 'None'} miles to ${
                                      values?.distance
                                    } miles`
                                  )
                              }}
                            />
                          </div>
                          {/* <span style={{ textTransform: 'lowercase' }}>{values.distance} miles away</span> ~/ */}
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox
                          alignItems="center"
                          gap="12px"
                          flexDirection="row-reverse"
                          justifyContent="space-between"
                        >
                          <p>{'Drive Time (mins)'}</p>

                          <div className="distance">
                            <InputWithValidation
                              labelName="Duration"
                              stateName="duration"
                              error={touched.duration && errors.duration ? true : false}
                              twoInput={true}
                              isSmall
                              onBlur={() => {
                                if (hasValueChanged(oppData?.duration, Number(values?.duration)))
                                  handleInputBlurValue(
                                    {
                                      duration: Number(values?.duration),
                                      PO: oppData?.PO,
                                      num: oppData?.num,
                                    },
                                    `changed Drive Time from ${oppData?.duration || 'None'} mins to ${
                                      values?.duration
                                    } mins`
                                  )
                              }}
                            />
                          </div>
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox
                          width="100%"
                          gap="12px"
                          alignItems="center"
                          className="subcontractor-hide"
                        >
                          <p>Lead source </p>

                          {/* <CustomSelect
                            labelName="Lead source"
                            stateName="leadSource"
                            error={touched.leadSource && errors.leadSource ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.leadSource!}
                            dropDownData={getKeysFromObjects(leadSrcDrop, 'name')?.sort()}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="0"
                            isSmall
                            onBlur={() => {
                              if (code !== 'referral') {
                                const leadSourceId = getIdFromName(values?.leadSource, leadSrcDrop)

                                if (hasValueChanged(oppData?.leadSourceId, leadSourceId))
                                  handleInputBlurValue(
                                    {
                                      leadSource: values?.leadSource ? values?.leadSource : '',
                                      leadSourceId: leadSourceId ? leadSourceId : undefined,
                                      PO: oppData?.PO,
                                      num: oppData?.num,
                                      selfGen: code === 'selfGen' ? true : false,
                                    },
                                    `changed Lead Source from ${oppData?.leadSource || 'None'} to ${values.leadSource}`
                                  )
                              }
                            }}
                          /> */}

                          <AutoCompleteIndentation
                            labelName=""
                            stateName={`leadSource`}
                            isLeadSource
                            dropdownHeight="300px"
                            error={touched.leadSource && errors.leadSource ? true : false}
                            borderRadius="0px"
                            setFieldValue={setFieldValue}
                            options={mergeSourceAndCampaignNames(leadSrcData)}
                            formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                            value={values.leadSource!}
                            setValueOnClick={(val: string) => {
                              setFieldValue('leadSource', val)

                              const code = getValueByKeyAndMatch('code', val, 'name', leadSrcDrop)

                              if (code !== 'referral') {
                                // const leadSourceId = getIdFromName(val, leadSrcDrop)

                                const resultSrc = getLeadSrcDropdownId(val, leadSrcData)
                                const leadSourceId = resultSrc?.leadSourceId
                                if (resultSrc?.campaignId) {
                                  if (hasValueChanged(oppData?.campaignId || null, resultSrc?.campaignId)) {
                                    handleInputBlurValue(
                                      {
                                        leadSource: resultSrc?.leadSourceName,
                                        leadSourceId: leadSourceId ? leadSourceId : undefined,

                                        PO: oppData?.PO,
                                        num: oppData?.num,
                                        selfGen: code === 'selfGen' ? true : false,
                                        campaignId: resultSrc?.campaignId,
                                        campaignName: resultSrc?.campaignName,
                                        referredBy: null,
                                      },
                                      `changed Campaign from ${oppData?.campaignName || 'None'} to ${
                                        resultSrc?.campaignName
                                      } and Lead Source from ${oppData?.leadSource} to ${resultSrc.leadSourceName}`
                                    )
                                  }
                                } else {
                                  // if (hasValueChanged(oppData?.leadSourceId, leadSourceId!))
                                  handleInputBlurValue(
                                    {
                                      leadSource: resultSrc?.leadSourceName,
                                      leadSourceId: leadSourceId ? leadSourceId : undefined,
                                      PO: oppData?.PO,
                                      num: oppData?.num,
                                      selfGen: code === 'selfGen' ? true : false,
                                      campaignId: null,
                                      campaignName: null,
                                      referredBy: null,
                                    },
                                    `changed Lead Source from ${oppData?.leadSource} to ${resultSrc.leadSourceName}`
                                  )
                                }
                              }
                            }}
                            className="lead-source"
                            isIndentation={true}
                          />
                        </SharedStyled.FlexBox>

                        {/* hardcoded code */}
                        {code === 'referral' && (
                          <SharedStyled.FlexBox
                            width="100%"
                            gap="12px"
                            alignItems="center"
                            className="subcontractor-hide"
                          >
                            <p>Referrer </p>
                            {/* <CustomSelect
                              labelName="Referrer"
                              stateName="referredBy"
                              error={touched.referredBy && errors.referredBy ? true : false}
                              setFieldValue={setFieldValue}
                              value={values.referredBy}
                              dropDownData={getKeysFromObjects(refererres, 'name')?.sort()}
                              setValue={() => {}}
                              innerHeight="52px"
                              margin="0"
                              isSmall
                              onBlur={() => {
                                const leadSourceId = getIdFromName(values?.leadSource, leadSrcDrop)

                                const referrerId =
                                  code === 'referral' ? getStageIdFromName(values.referredBy, refererres) : undefined

                                if (hasValueChanged(oppData?.referredBy, referrerId))
                                  handleInputBlurValue(
                                    {
                                      leadSourceId: leadSourceId ? leadSourceId : undefined,
                                      referredBy: referrerId,
                                      PO: oppData?.PO,
                                      num: oppData?.num,
                                      selfGen: code === 'selfGen' ? true : false,
                                      leadSource: values?.leadSource,
                                      campaignId: null,
                                      campaignName: null,
                                    },
                                    `changed Referrer from ${oppData?.referrer?.name || 'None'} to ${values.referredBy}`
                                  )
                              }}
                            /> */}

                            {/* hardcoded code */}

                            <SharedStyled.FlexRow margin='0 0 0 10px'>
                              <SearchableDropdown
                                label=""
                                className="referral"
                                placeholder="Type to search"
                                searchFunction={(val) => {
                                  return fetchSearchReferer(val, false)
                                }}
                                displayKey={'name'}
                                refererOptions={refererres?.slice(0, 20)}
                                onSelect={(item: any) => {
                                  setFieldValue('referredBy', item?.name)

                                  const leadSourceId = getIdFromName(values?.leadSource, leadSrcDrop)
                                  {
                                    /* hardcoded code */
                                  }
                                  const referrerId =
                                    code === 'referral' ? getStageIdFromName(item?.name, refererres) : undefined

                                  if (hasValueChanged(oppData?.referredBy, referrerId))
                                    handleInputBlurValue(
                                      {
                                        leadSourceId: leadSourceId ? leadSourceId : undefined,
                                        referredBy: referrerId,
                                        PO: oppData?.PO,
                                        num: oppData?.num,
                                        selfGen: code === 'selfGen' ? true : false,
                                        leadSource: values?.leadSource,
                                        campaignId: null,
                                        campaignName: null,
                                      },
                                      `changed Referrer from ${oppData?.referrer?.name || 'None'} to ${item?.name}`
                                    )
                                }}
                                selectedValue={values.referredBy}
                                resultExtractor={(res) => res?.data?.data?.referrers || []}
                              />
                            </SharedStyled.FlexRow>
                          </SharedStyled.FlexBox>
                        )}

                        {/* {oppData?.referrer && (
                        <SharedStyled.FlexRow
                          alignItems="center"
                          width="100%"
                          gap="12px"
                          flexDirection="row-reverse"
                          justifyContent="space-between"
                        >
                          <p>Referrer</p>
                          <span className="bold" style={{ textTransform: 'lowercase' }}>
                            {oppData?.referrer?.name || '--'}
                          </span>
                        </SharedStyled.FlexRow>
                      )} */}
                        {
                          // values.oppState.includes(' WA') ? (
                          // taxUniqueState.some((state: any) => state === values.oppState) ? (
                          // getValueByKeyAndMatch('rate', oppData.taxJurisdiction, '_id', formatedTax) !== 0 && (  // uncomment this when dev is merged
                          <SharedStyled.FlexBox gap="12px" className="subcontractor-hide">
                            <p>
                              <a
                                href="https://webgis.dor.wa.gov/taxratelookup/SalesTax.aspx"
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ fontSize: '13px', color: colors.darkBlue }}
                              >
                                Tax Jurisdiction
                              </a>
                            </p>
                            <SharedStyled.TooltipContainer
                              width={'200px'}
                              // positionLeft="0px"
                              // positionBottom="0px"
                              // positionLeftDecs={positionLeftDecs}
                              positionBottomDecs="100%"
                              className="no-pointer"
                            >
                              <CustomSelect
                                labelName="Tax Jurisdiction"
                                stateName="taxJurisdiction"
                                error={touched.taxJurisdiction && errors.taxJurisdiction ? true : false}
                                setFieldValue={setFieldValue}
                                value={values.taxJurisdiction}
                                dropDownData={tax?.map(
                                  (v: any) =>
                                    `${v?.name} ${
                                      v?.rate !== undefined && v?.rate !== null && !isNaN(v?.rate) && v?.rate > 0
                                        ? `(${v.rate}%)`
                                        : ''
                                    }`
                                )}
                                // dropDownData={`${getKeysFromObjects(tax, 'city')}, ${getKeysFromObjects(tax, 'state')} (${getKeysFromObjects(tax, 'rate')}%)`}
                                setValue={() => {}}
                                innerHeight="52px"
                                margin="0"
                                isSmall
                                onBlur={() => {
                                  let value = ''
                                  if (taxUniqueState.some((state: any) => values.oppState.includes(state))) {
                                    value = getTaxJurisdictionIdFromName(values.taxJurisdiction, formatedTax)
                                  } else {
                                    value = ''
                                  }
                                  if (hasValueChanged(oppData?.taxJurisdiction, value))
                                    handleInputBlurValue(
                                      {
                                        taxJurisdiction: value,
                                        PO: oppData?.PO,
                                        num: oppData?.num,
                                      },
                                      `changed Tax Jurisdiction from ${
                                        getTaxJurisdictionNameFromId(oppData?.taxJurisdiction, formatedTax) || 'None'
                                      } to ${values.taxJurisdiction}`
                                    )
                                }}
                              />
                            </SharedStyled.TooltipContainer>
                          </SharedStyled.FlexBox>
                          // )
                          // ) : null
                          // setFieldValue('taxJurisdiction', '')
                        }
                        <SharedStyled.FlexBox
                          width="100%"
                          alignItems="center"
                          gap="12px"
                          className="subcontractor-hide"
                        >
                          <p>Stage </p>
                          <CustomSelect
                            labelName="Stage"
                            stateName="stage"
                            error={touched.stage && errors.stage ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.stage}
                            dropDownData={getKeysFromObjects(stages, 'name')}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="0"
                            isSmall
                            onBlur={() => {
                              const stage = getStageIdFromName(values.stage, stages)
                              const matchedStageObject = stages.find((v) => v._id === stage)
                              if (hasValueChanged(oppData?.stage, stage))
                                handleInputBlurValue(
                                  {
                                    stage,
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                  },
                                  `changed Stage from ${getStageNameFromId(oppData?.stage!, stages) || 'None'} to ${
                                    values.stage
                                  }`,
                                  () => {
                                    navigate(`/${getEnumValue(matchedStageObject.stageGroup)}/opportunity/${oppId}`)
                                  }
                                )
                            }}
                          />
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox
                          width="100%"
                          gap="12px"
                          alignItems="center"
                          className="subcontractor-hide"
                        >
                          <p>Lead Type</p>
                          <CustomSelect
                            labelName="Type"
                            stateName="type"
                            error={touched.type && errors.type ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.type!}
                            dropDownData={projectTypesDrop.map(({ name }: { name: string }) => name)}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="0"
                            isSmall
                            onBlur={() => {
                              const type = getIdFromName(values.type, projectTypesDrop)

                              if (hasValueChanged(oppData?.oppType, type))
                                handleInputBlurValue(
                                  {
                                    oppType: type,
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                  },
                                  `changed Lead Type from ${
                                    getNameFromId(oppData?.oppType, projectTypesDrop) || 'None'
                                  } to ${values.type}`
                                )
                            }}
                          />
                        </SharedStyled.FlexBox>
                        {contractOrder.length > 0 && (
                          <SharedStyled.FlexRow
                            alignItems="center"
                            width="100%"
                            gap="12px"
                            flexDirection="row-reverse"
                            justifyContent="space-between"
                          >
                            <p>Project Type</p>
                            <span className="bold">{getNameFromId(oppData?.acceptedType, projectTypesDrop)}</span>
                          </SharedStyled.FlexRow>
                        )}

                        <SharedStyled.FlexBox
                          width="100%"
                          gap="12px"
                          alignItems="center"
                          className="subcontractor-hide"
                        >
                          <p>CSR</p>
                          <CustomSelect
                            labelName="Assigned To"
                            stateName="csr"
                            error={touched.csr && errors.csr ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.csr!}
                            dropDownData={getKeysFromObjects(officeDrop, 'name')}
                            setValue={() => {}}
                            margin="0"
                            isSmall
                            onBlur={() => {
                              const officePersonId = getSalesPersonIdFromName(values.csr, officeDrop) || oppData?.csrId

                              if (hasValueChanged(oppData?.csrId, officePersonId))
                                handleInputBlurValue(
                                  {
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                    csrId: officePersonId,
                                    CSRAssigned: values.csr,
                                  },
                                  `changed Office Person from ${
                                    getNameFrom_Id(oppData?.csrId, officeDrop) || 'None'
                                  } to ${values.csr}`
                                )
                            }}
                          />
                        </SharedStyled.FlexBox>

                        <SharedStyled.FlexBox
                          width="100%"
                          gap="12px"
                          alignItems="center"
                          className="subcontractor-hide"
                        >
                          <p>Sales Person </p>
                          <CustomSelect
                            labelName="Assigned To"
                            stateName="assignedTo"
                            error={touched.assignedTo && errors.assignedTo ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.assignedTo!}
                            dropDownData={getKeysFromObjects(salesPersonDrop, 'name')}
                            setValue={() => {}}
                            margin="0"
                            isSmall
                            onBlur={() => {
                              const salesPersonId =
                                getSalesPersonIdFromName(values.assignedTo, salesPersonDrop) || oppData?.salesPerson

                              if (hasValueChanged(oppData?.salesPerson, salesPersonId))
                                handleInputBlurValue(
                                  {
                                    salesPerson: salesPersonId,
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                  },
                                  `changed Sales Person from ${
                                    getNameFrom_Id(oppData?.salesPerson, salesPersonDrop) || 'None'
                                  } to ${values.assignedTo}`
                                )
                            }}
                          />
                        </SharedStyled.FlexBox>
                        {curStage?.name === 'Process Contract' || operationsFlag ? (
                          <SharedStyled.FlexBox gap="12px" width="100%" alignItems="center">
                            <p className="bold">Project Manager</p>
                            <CustomSelect
                              labelName="Project Manager"
                              stateName="projectManager"
                              error={touched.projectManager && errors.projectManager ? true : false}
                              setFieldValue={setFieldValue}
                              value={values.projectManager!}
                              dropDownData={getKeysFromObjects(salesManagerDrop, 'name')}
                              setValue={setProjectManagerName}
                              margin="0"
                              isSmall
                              onBlur={() => {
                                if (projectManagerName !== '') {
                                  const projectManagerId =
                                    getSalesPersonIdFromName(projectManagerName, salesManagerDrop) ||
                                    oppData?.projectManager

                                  if (hasValueChanged(oppData?.projectManager, projectManagerId))
                                    handleInputBlurValue(
                                      {
                                        projectManager: projectManagerId === '' ? null : projectManagerId,
                                        PO: oppData?.PO,
                                        num: oppData?.num,
                                      },
                                      `changed Project Manager from ${
                                        getNameFrom_Id(oppData?.projectManager, salesManagerDrop) || 'None'
                                      } to ${projectManagerName}`
                                    )
                                }
                              }}
                            />
                          </SharedStyled.FlexBox>
                        ) : (
                          <></>
                        )}
                        <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                          <p>Crew</p>
                          <CustomSelect
                            labelName="Pick one..."
                            stateName="workingCrew"
                            error={touched.workingCrew && errors.workingCrew ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.workingCrew!}
                            dropDownData={getKeysFromObjects(crewSub, 'name')}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="0"
                            isSmall
                            onBlur={() => {
                              const workingCrew = crewSub?.find((v: any) => v.name === values.workingCrew)

                              if (hasValueChanged(oppData?.workingCrew?.id, workingCrew?.id))
                                handleInputBlurValue(
                                  {
                                    workingCrew: workingCrew,
                                    PO: oppData?.PO,
                                    num: oppData?.num,
                                  },
                                  `changed Crew from ${getNameFromId(oppData?.workingCrew?.id, crewSub) || 'None'} to ${
                                    values.workingCrew
                                  }`
                                )
                            }}
                          />
                        </SharedStyled.FlexBox>
                        {/* {operationsFlag && ( */}
                        <>
                          {/* ============== Sales commission ============== */}

                          {oppData?.saleDate ? (
                            <>
                              {contractOrder.length > 0 ? (
                                <>
                                  <SharedStyled.FlexBox
                                    width="100%"
                                    gap="12px"
                                    alignItems="center"
                                    className="subcontractor-hide"
                                  >
                                    <p>Discount </p>

                                    <span className="bold">
                                      {oppData?.discount ? `$${oppData?.discount}` : '$ --'}{' '}
                                      {oppData?.discount && oppData?.soldValue
                                        ? `(${(
                                            (oppData?.discount * 100) /
                                            (oppData?.soldValue + oppData?.discount)
                                          ).toFixed(1)}%)`
                                        : '(0%)'}
                                    </span>
                                  </SharedStyled.FlexBox>

                                  <SharedStyled.FlexBox
                                    width="100%"
                                    gap="12px"
                                    alignItems="center"
                                    className="subcontractor-hide"
                                  >
                                    <p>Sold Value </p>
                                    <span className="bold">
                                      ${oppData?.soldValue ? formatNumberToCommaS(oppData?.soldValue) : `--`}
                                    </span>
                                  </SharedStyled.FlexBox>
                                  <SharedStyled.FlexBox
                                    width="100%"
                                    gap="12px"
                                    alignItems="center"
                                    className="subcontractor-hide"
                                  >
                                    <p>Payment Type</p>
                                    <span className="bold">{oppData?.paymentType ? oppData?.paymentType : `--`}</span>
                                  </SharedStyled.FlexBox>
                                  {FinanceFeeVisibleTo?.includes(oppData?.paymentType) ? (
                                    <SharedStyled.FlexBox
                                      width="100%"
                                      gap="12px"
                                      alignItems="center"
                                      className="subcontractor-hide"
                                    >
                                      <p>Finance Fees ($)</p>

                                      <div className="distance">
                                        <InputWithValidation
                                          labelName="Finance fees"
                                          stateName="financeFee"
                                          error={touched.financeFee && errors.financeFee ? true : false}
                                          twoInput={true}
                                          isSmall
                                          onBlur={() => {
                                            if (hasValueChanged(oppData?.financeFee, Number(values?.financeFee)))
                                              handleInputBlurValue(
                                                {
                                                  financeFee: Number(values?.financeFee),
                                                  PO: oppData?.PO,
                                                  num: oppData?.num,
                                                },
                                                `changed Finance Fees from $${oppData?.financeFee ?? 0} to $${
                                                  values?.financeFee
                                                }`
                                              )
                                          }}
                                        />
                                      </div>
                                    </SharedStyled.FlexBox>
                                  ) : null}
                                </>
                              ) : null}

                              {isRestrictedOpp ? null : (
                                <SalesCommission
                                  oppData={oppData}
                                  initFetch={initFetch}
                                  setAddCommission={setAddCommission}
                                  setCommissionEdit={setCommissionEdit}
                                  salesCommissionData={salesCommissionData}
                                  setCommissionUpdate={setCommissionUpdate}
                                />
                              )}
                            </>
                          ) : null}

                          {isLost && (
                            <>
                              <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                                <p>Lost Date</p>
                                <span className="bold">
                                  {oppData?.lostDate ? dayjsFormat(oppData?.lostDate, 'M/D/YY') : `--`}
                                </span>
                              </SharedStyled.FlexBox>
                              <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                                <p>Lost Reason</p>

                                <SharedStyled.TooltipContainer
                                  width="200px"
                                  positionLeft="0"
                                  positionBottom="0"
                                  positionLeftDecs="100px"
                                  positionBottomDecs="25px"
                                >
                                  <span className="tooltip-content">{oppData?.lostReason}</span>
                                  <span className="bold">{truncateParagraph(oppData?.lostReason, 25)}</span>
                                </SharedStyled.TooltipContainer>
                              </SharedStyled.FlexBox>
                            </>
                          )}

                          {allSteps && renderAllSteps(allSteps, isRestrictedOpp)}

                          {/* {allSteps &&
                          Object.entries(allSteps).map(
                            ([key, value]) =>
                              value &&
                              value !== '0' && (
                                <SharedStyled.FlexRow
                                  key={key}
                                  flexDirection="row-reverse"
                                  justifyContent="space-between"
                                >
                                  <p>{key}</p>
                                  <span className="bold">
                                    {typeof value === 'string' &&
                                    /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/.test(value)
                                      ? dayjsFormat(value, 'M/D/YY')
                                      : typeof value === 'number'
                                      ? `$${formatNumberToCommaS(Number(value)) as React.ReactNode}`
                                      : value}
                                  </span>
                                </SharedStyled.FlexRow>
                              )
                          )} */}

                          {viewJobReportArray?.includes(values?.stage) && !isRestrictedOpp ? (
                            <Link
                              to={`/reports/projectReport?pId=${oppData?.acceptedProjectId}&oppId=${oppData?._id}`}
                              style={{ fontSize: '13px', color: colors.darkBlue }}
                            >
                              View Job Cost Report &gt;&gt;
                            </Link>
                          ) : (
                            ''
                          )}

                          <Styled.ButtonsWrapper>
                            {/* <Button type="button" disabled={loading} onClick={() => !loading && handleSubmit()}>
                                Save Changes
                              </Button> */}

                            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                              <Button
                                className={isInactive ? 'success' : 'gray'}
                                type="button"
                                tooltip={
                                  !oppData?.saleDate ? '' : `Can’t inactivate this Opportunity because it has been sold`
                                }
                                tooltipWidth={'230px'}
                                disabled={!oppData?.saleDate ? false : true}
                                onClick={() => {
                                  if (!isInactive) {
                                    let confirmed = window.confirm(
                                      'Are you sure you want to make this Opportunity Inactive?'
                                    )
                                    if (!confirmed) {
                                      return
                                    }
                                  }
                                  makeInactive()
                                }}
                              >
                                {`Make ${isInactive ? 'active' : 'inactive'}`}
                              </Button>
                              <Button
                                type="button"
                                tooltip={
                                  !oppData?.saleDate ? '' : `Can’t lose this Opportunity because it has been sold`
                                }
                                tooltipWidth={'230px'}
                                disabled={!oppData?.saleDate ? false : true}
                                className={isLost ? 'success' : 'delete'}
                                onClick={makeLost}
                              >
                                {isLost ? 'Unlost' : 'Lost'}
                              </Button>
                            </div>
                          </Styled.ButtonsWrapper>
                        </>
                        {/* )} */}
                      </div>

                      <CustomModal show={showDriveTimeModal}>
                        <DriveTimeModal
                          onClose={async () => {
                            setShowDriveTimeModal(false)

                            const name =
                              oppData?.contact?.lastName && oppData?.contact?.lastName !== ''
                                ? oppData?.contact?.lastName
                                : oppData?.contact?.firstName

                            let oppLat: string = ''
                            let oppLong: string = ''

                            if (values?.street && values?.oppCity && values?.zip && values?.oppState) {
                              try {
                                const { lat, lng } = await getLatLongFromAddress(
                                  `${values?.street || ''}, ${values?.oppCity || ''}, ${values?.oppState || ''} ${
                                    values?.zip || ''
                                  }, USA`
                                )

                                oppLat = lat || ''
                                oppLong = lng || ''
                                console.log('longitude', lat, lng)
                              } catch (geocodeError) {
                                console.log('Failed to fetch latitude and longitude:', geocodeError)
                                oppLat = ''
                                oppLong = ''
                              }
                            }
                            await handleInputBlurValue(
                              {
                                street: values?.street,
                                city: values?.oppCity,
                                state: values?.oppState,
                                zip: values?.zip,
                                duration: values?.duration,
                                distance: values?.distance,
                                oppLat: `${oppLat}` || undefined,
                                oppLong: `${oppLong}` || undefined,
                                PO: createPO(name, values?.street),
                                num: '01',
                              },
                              `updated Address from ${oppData?.street}, ${oppData?.city}, ${oppData?.state}, ${oppData?.zip} to ${values?.street}, ${values?.oppCity}, ${values?.oppState}, ${values?.zip}`,
                              () => {},
                              true
                            )

                            setToggleAddress(false)
                            setAddressInputType('google')
                            setDistance(values?.distance)
                          }}
                          duration={values?.duration}
                          distance={values?.distance}
                          setFieldValue={setFieldValue}
                        />
                      </CustomModal>
                    </>
                  )
                }}
              </Formik>
            </Styled.PoContainer>

            {/* ===================== Crew Sheet ===================== */}

            {!isRestrictedOpp ? null : (
              <>
                <Button
                  onClick={() => {
                    const printContent = document.querySelector('#print-div-crew-sheet')
                    if (printContent) {
                      const clone = printContent.cloneNode(true)
                      document.body.innerHTML = ''
                      document.body.appendChild(clone)
                      window.print()
                      window.location.reload()
                    }
                  }}
                >
                  Crew sheet
                </Button>

                <div id="print-div-crew-sheet">
                  {contractOrder?.[0]?.projects?.map((project: any, index: number) => {
                    const projectTypeData = projectTypes?.find((itm: { _id: string }) => itm?._id === project?.type)

                    const filteredMaterial1 = material1.filter((v) => v?.projectId === project?.projectId)
                    const filteredMaterial2 = material2.filter((v) => v?.projectId === project?.projectId)
                    // const filteredMaterial3 = material3?.filter((v) => v?.projectId === project?.projectId)

                    // const totalLaborCost = values?.projects.reduce((acc, p) => {
                    //   return acc + (p?.projectId !== project?.projectId && p?.priceTotals?.laborCost || 0)
                    // }, 0)
                    // console.log({totalLaborCost},project.name)
                    return (
                      <>
                        <div style={{ pageBreakBefore: 'always' }}></div>

                        <>
                          <SharedStyled.FlexRow margin="0 0 8px 0">
                            <SharedStyled.Text font-weight={'500'} fontSize="21px">
                              PO#: {oppData?.PO}-{oppData?.num}
                            </SharedStyled.Text>
                          </SharedStyled.FlexRow>

                          <>
                            <SharedStyled.FlexBox width="50%" justifyContent="space-between">
                              <div>
                                <SharedStyled.Text fontSize="14px" className="capitalize">
                                  {oppData?.contact?.fullName}
                                  {/* {oppData?.firstName} {oppData?.lastName}  */}
                                  <br />
                                  {oppData?.street}
                                  <br />
                                  {oppData?.city}, {oppData?.state} {oppData?.zip}
                                </SharedStyled.Text>
                              </div>
                              <div>
                                <SharedStyled.Text fontSize="14px">
                                  {(oppData?.contact?.phone && formatPhoneNumber(oppData?.contact?.phone, '')) || 'N/A'}
                                  <br />

                                  <br />
                                  {oppData?.duration > 0
                                    ? oppData?.duration === 1
                                      ? `${oppData?.duration} minute away`
                                      : `${oppData?.duration} minutes away`
                                    : ''}
                                </SharedStyled.Text>
                              </div>
                            </SharedStyled.FlexBox>
                            <SharedStyled.HorizontalDivider margin={'10px 0'} />
                          </>
                        </>

                        <SharedStyled.FlexCol gap="8px" margin="6px 0 14px 10px">
                          <SharedStyled.Text
                            textDecoration="underline"
                            textTransform="capitalize"
                            fontWeight="700"
                            fontSize="24px"
                          >
                            {`${index + 1}) ${project?.name}`}
                          </SharedStyled.Text>
                          <>
                            <BoxGap display="flex" margin="0 0 -5px 20px">
                              <SharedStyled.Text fontWeight="600" fontSize="14px">
                                Notes:
                              </SharedStyled.Text>
                            </BoxGap>

                            <>
                              <SharedStyled.Text margin="0 0 0 30px">
                                <PrintNotesContainer>
                                  <ReactMarkdown>
                                    {contractOrder?.[0]?.projects?.[index]?.notes?.replace(/\n/g, '  \n')}
                                  </ReactMarkdown>
                                </PrintNotesContainer>
                                {/* <SharedStyled.Text fontSize="14px">{orderById?.notes}</SharedStyled.Text> */}
                              </SharedStyled.Text>
                            </>
                          </>

                          <>
                            <BoxGap display="flex" margin="0px 0 -8px 20px">
                              <SharedStyled.Text fontWeight="600" fontSize="14px">
                                Colors:
                              </SharedStyled.Text>
                            </BoxGap>

                            <>
                              <BoxGap style={{ gap: 0 }}>
                                {renderColors(contractOrder?.[0]?.projects?.[index]?.colors, projectTypeData)}
                              </BoxGap>
                            </>
                          </>

                          <div>
                            <SharedStyled.Text margin="20px 0 0 10px" fontWeight="600" fontSize="18px">
                              Work Order
                            </SharedStyled.Text>
                          </div>

                          <BoxGap margin="0 0 0 20px">
                            {contractOrder?.[0]?.projects?.[index]?.avgPitch ? (
                              <SharedStyled.Text fontSize="14px" margin="10px 0 0 0px">
                                Avg Pitch: {contractOrder?.[0]?.projects?.[index]?.avgPitch}/12
                              </SharedStyled.Text>
                            ) : (
                              ''
                            )}
                            {contractOrder?.[0]?.projects?.[index]?.pitch ? (
                              <SharedStyled.Text fontSize="14px" margin="10px 0 0 0px">
                                Pitch: {contractOrder?.[0]?.projects?.[index]?.pitch}/12
                              </SharedStyled.Text>
                            ) : (
                              ''
                            )}
                            {renderCrew(
                              (project?.laborTime || 0) +
                                (project?.priceTotals?.travelFee / contractOrder?.[0]?.priceTotals?.travelHrlyRate ||
                                  0) || 0
                            )}
                          </BoxGap>
                        </SharedStyled.FlexCol>

                        <BoxGap key={project.type} margin="0 0 20px 40px">
                          <BoxGap width="80%">
                            <BoxGap width="100%" display="flex" border={true}>
                              <BoxGap className="three-table-div1" width="80%">
                                <SharedStyled.Text fontSize="14px">
                                  <b>Task</b>
                                </SharedStyled.Text>
                              </BoxGap>
                              <BoxGap textAlign="center" className="three-table-div2" width="10%">
                                <SharedStyled.Text fontSize="14px">
                                  <b>Amt</b>
                                </SharedStyled.Text>
                              </BoxGap>
                              <BoxGap textAlign="center" className="three-table-div3" width="10%">
                                <SharedStyled.Text fontSize="14px">
                                  <b>Unit</b>
                                </SharedStyled.Text>
                              </BoxGap>
                            </BoxGap>
                          </BoxGap>

                          {sortTasks(project?.workOrder)
                            ?.filter((v) => v.title !== 'Travel')
                            ?.map(
                              (
                                {
                                  taskName,
                                  taskUnit,
                                  rawValue,
                                }: { taskName: string; taskUnit: string; rawValue: number },
                                orderIndex: number
                              ) => {
                                return (
                                  <BoxGap width="80%" key={orderIndex}>
                                    <BoxGap width="100%" display="flex" border={true}>
                                      <BoxGap className="three-table-div1" width="80%">
                                        <SharedStyled.Text fontSize="14px">{taskName}</SharedStyled.Text>
                                      </BoxGap>
                                      <BoxGap textAlign="right" className="three-table-div2" width="10%">
                                        <SharedStyled.Text fontSize="14px">
                                          {roundToNearestTenth(rawValue)?.toFixed(1)}
                                        </SharedStyled.Text>
                                      </BoxGap>
                                      <BoxGap textAlign="center" className="three-table-div3" width="10%">
                                        <SharedStyled.Text fontSize="14px">{taskUnit}</SharedStyled.Text>
                                      </BoxGap>
                                    </BoxGap>
                                  </BoxGap>
                                )
                              }
                            )}
                        </BoxGap>

                        {
                          <>
                            <SharedStyled.FlexCol gap="8px" margin="0 0 0 20px">
                              <SharedStyled.Text fontWeight="600" margin="0 0 10px 10px" fontSize="14px">
                                Bring From Shop{' '}
                              </SharedStyled.Text>
                            </SharedStyled.FlexCol>

                            <div>
                              {project.originalMatList
                                ? materialTable(filteredMaterial1, project.originalMatList, true)
                                : true
                                ? materialTable(filteredMaterial1, project.originalMatList, true)
                                : null}
                              <SharedStyled.FlexCol gap="8px" margin="0 0 0 20px">
                                <SharedStyled.Text fontWeight="600" margin="0 0 10px 10px" fontSize="14px">
                                  Order from Supplier{' '}
                                </SharedStyled.Text>
                              </SharedStyled.FlexCol>
                              {project.originalMatList
                                ? materialTable(filteredMaterial2, project.originalMatList, true)
                                : true
                                ? materialTable(filteredMaterial2, project.originalMatList, true)
                                : null}
                            </div>
                          </>
                        }
                      </>
                    )
                  })}
                </div>
              </>
            )}
          </SharedStyled.FlexCol>

          {/* ========================================= Projects ========================================= */}
          {isRestrictedOpp ? null : (
            <SharedStyled.FlexBox
              gap="24px"
              alignItems="flex-start"
              width="100%"
              flexWrap={width < 1024 ? 'wrap' : 'nowrap'}
            >
              <SharedStyled.FlexCol gap="34px" className="custom-styles half-width">
                {
                  <>
                    {(oppData?.taxJurisdiction === '' || !oppData?.taxJurisdiction) &&
                    taxUniqueState?.some((state: any) => oppData?.state?.includes(state)) ? (
                      <div className="state-error">
                        <SharedStyled.Text fontSize="12px">Must Select Tax Jurisdiction</SharedStyled.Text>
                      </div>
                    ) : companySettingForAll?.workingStates?.includes(oppData?.state) ? (
                      <></>
                    ) : (
                      <div className="state-error">
                        <SharedStyled.Text fontSize="12px">
                          The selected state in the oppData is invalid. Please choose a valid state
                        </SharedStyled.Text>
                      </div>
                    )}
                  </>
                }
                <SharedStyled.FlexRow alignItems="flex-start">
                  <SharedStyled.ContentHeader
                    as={'h3'}
                    alignItems="center"
                    textAlign="left"
                    display="flex"
                    width="max-content"
                  >
                    Projects
                  </SharedStyled.ContentHeader>
                  <Button
                    className="fit"
                    onClick={() => {
                      duration > 0
                        ? navigate(
                            `/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/new-project/${
                              oppData.contactId
                            }`
                          )
                        : notify('Drive Time must be entered before creating a project', 'error')
                      // setShowAddProjectModal(true)
                    }}
                    disabled={disableForTax}
                  >
                    New Project
                  </Button>
                </SharedStyled.FlexRow>
                <Styled.PoContainer>
                  {projectLoading ? (
                    <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} custMarginTop={'50px'} />
                  ) : (
                    <Table
                      columns={columns}
                      data={projectTableValues}
                      loading={projectLoading}
                      pageCount={pageCount}
                      fetchData={() => {}}
                      hideHeader
                      onRowClick={(vals) => {
                        // vals?.orderId ? (
                        //   <div></div>
                        // ) : (
                        navigate(
                          `/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/contract/${vals._id}`
                        )
                        // )
                      }}
                      // noLink={true}
                      noSearch
                      minWidth=""
                      noBorder
                      activeItem={contractOrder[0]?._id}
                      padding={'10px 24px'}
                    />
                  )}
                  {contractOrder.length > 0 && (
                    <Styled.ShowContract
                      onClick={() =>
                        navigate(
                          `/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/orderDetail/${
                            contractOrder[0]?._id
                          }`
                        )
                      }
                    >
                      View Order For {contractOrder[0]?.projects[0]?.name}
                    </Styled.ShowContract>
                  )}
                  {operationsFlag && (
                    <DailyLogBox
                      priceId={contractOrder?.[0]?.projectPriceId}
                      oppData={oppData}
                      setOppData={setOppData}
                      currentStage={getStageNameFromId(oppData?.stage!, stages)}
                      setCommissionUpdateBool={setCommissionUpdateBool}
                      fetchActivity={fetchActivity}
                    />
                  )}
                </Styled.PoContainer>
              </SharedStyled.FlexCol>
            </SharedStyled.FlexBox>
          )}
        </SharedStyled.FlexRow>

        <SharedStyled.FlexBox
          width="100%"
          gap="24px"
          justifyContent="space-between"
          alignItems="flex-start"
          // flexWrap="wrap"
          flexDirection="row"
          className="container"
        >
          {/* ========================================= Todo ========================================= */}

          {isRestrictedOpp ? null : (
            <Styled.TodoWrapper className="custom-styles">
              {/* <SharedStyled.FlexCol gap="20px"> */}
              {/* <ToDoNext
                oppData={oppData}
                oppId={oppId}
                initFetch={initFetch}
                setOppData={setOppData}
                fetchActivity={fetchActivity}
              /> */}

              <ToDoNextProfile
                contactData={oppData}
                contactOrOppId={oppId}
                // initFetch={initFetch}
                setContactData={() => {}}
                fetchActivity={fetchActivity}
              />

              {/* ========================================= Assessment Form ========================================= */}

              {!stepForToDoNextFlag ? (
                // {checkListData !== undefined && (
                <ErrorBoundary>
                  <AssessmentForm
                    checkpoints={checkpoints}
                    stages={stages}
                    oppData={oppData}
                    setOppData={setOppData}
                    initFetch={initFetch}
                    initFetchOpportunity={initFetchOpportunity}
                    setInputKeys={setInputKeys}
                    operationsFlag={operationsFlag}
                    projectTableValues={projectTableValues}
                    contractOrder={contractOrder}
                    projectManager={initClientData.projectManager}
                    salesManagerDrop={salesManagerDrop}
                    // setProjectManagerCheckbox={setProjectManagerCheckbox}
                    // projectManagerCheckbox={projectManagerCheckbox}
                    setProjectManagerName={setProjectManagerName}
                    projectManagerName={projectManagerName}
                    fetchActivity={fetchActivity}
                    // setStepForToDoNext={setStepForToDoNext}
                    // setStepForToDoNextSuccessFLag={setStepForToDoNextSuccessFLag}
                    setStepForToDoNextFLag={setStepForToDoNextFLag}
                    setStepObject={setStepObject}
                    checkListData={checkListData}
                    fetchUpdateStepChecklist={fetchUpdateStepChecklist}
                    stepForToDoNextFlag={stepForToDoNextFlag}
                  />
                </ErrorBoundary>
              ) : (
                <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} custMarginTop={'50px'} />
              )}
              {/* </SharedStyled.FlexCol> */}
            </Styled.TodoWrapper>
          )}
          {/* ========================================= Comments and Activity  ========================================= */}

          <SharedStyled.FlexBox
            gap="24px"
            justifyContent="flex-start"
            alignItems="flex-start"
            width="100%"
            flexDirection="column"
            flexWrap={width < 1024 ? 'wrap' : 'nowrap'}
            className="custom-styles half-width"
          >
            {/* <Dates
            checkpoints={checkpoints}
            oppData={oppData}
            setOppData={setOppData}
            curStage={curStage}
          /> */}

            {/* </SharedStyled.FlexBox> */}

            <ErrorBoundary>
              <Comments comments={oppData?.comments ?? []} initFetchOpportunity={initFetchOpportunity} />
            </ErrorBoundary>
            {isRestrictedOpp ? null : (
              <Activity
                activities={oppData?.activities ?? []}
                inputKeys={inputKeys}
                stages={stages}
                getAtivity={getAtivity}
                activityLoading={activityLoading}
              />
            )}
          </SharedStyled.FlexBox>
        </SharedStyled.FlexBox>

        {isRestrictedOpp ? null : (
          <SharedStyled.FlexBox width="100%" flexDirection="column" alignItems="flex-start" gap="12px">
            <DeletedProjects fetchProjectActive={fetchProject} projectTypesDrop={projectTypesDrop} />
          </SharedStyled.FlexBox>
        )}

        <CustomModal show={lostModal}>
          <LostModal
            onClose={() => setLostModal(false)}
            isLost={isLost}
            setIsLost={setIsLost}
            setOppData={setOppData}
            checkpointSymbol={checkpoints?.map((v) => v.symbol)}
            oppData={oppData}
            fetchActivity={fetchActivity}
            initFetchOpportunity={initFetchOpportunity}
          />
        </CustomModal>
        <CustomModal show={showAddProjectModal}>
          <NewProject
            onClose={() => setShowAddProjectModal(false)}
            onSuccess={() => {
              setShowAddProjectModal(false)
            }}
          />
        </CustomModal>
        <CustomModal show={addCommission}>
          <CommissionModal
            onComplete={() => {
              setAddCommission(false)
              setCommissionUpdate({})
              fetchActivity()
            }}
            id={commissionEdit ? '12' : ''}
            salesPersonId={oppData?.salesPerson}
            memberId={currentMember._id}
            isEdit={commissionEdit}
            commissionUpdate={commissionUpdate}
            fetchOpportunityModifiedCommission={fetchOpportunityModifiedCommission}
          />
        </CustomModal>
        {deletedFlag || isRestrictedOpp ? null : (
          <SharedStyled.FlexRow justifyContent="flex-end">
            <Button
              onClick={handleDeleteOpportunity}
              // disabled={!!projectTableValues?.length}
              className="delete"
              maxWidth="180px"
              isLoading={btnLoading}
            >
              Delete Opportunity
            </Button>
          </SharedStyled.FlexRow>
        )}
      </Styled.OppContainer>
    </LoadScript>
  ) : null
}
export default Opportunity
