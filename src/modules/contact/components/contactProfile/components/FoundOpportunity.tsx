import React from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import Button from '../../../../../shared/components/button/Button'
import { getEnumValue } from '../../../../../shared/helpers/util'
import { useNavigate } from 'react-router-dom'

interface Opportunity {
  PO: string
  num: string
  oppType: {
    name: string
  }
  stage: { stageGroup: string }
  _id: string
}

interface InfoOpportunityModalProps {
  onClose: () => void
  onConfirm: () => void
  contactName: string
  opportunities: Opportunity[]
}

const FoundOpportunity: React.FC<InfoOpportunityModalProps> = ({ onClose, onConfirm, contactName, opportunities }) => {
  const navigate = useNavigate()
  return (
    <div style={{ width: '450px' }}>
      <SharedStyled.SettingModalContentContainer>
        <SharedStyled.Content maxWidth="512px" width="100%" disableBoxShadow={true} noPadding={true}>
          <SharedStyled.FlexCol gap="18px" alignItems="center">
            <SharedStyled.Text width="100%" fontSize="18px" textAlign="center" fontWeight="600">
              {contactName} already has {opportunities.length} open{' '}
              {opportunities.length === 1 ? 'opportunity' : 'opportunities'}:
            </SharedStyled.Text>

            {opportunities.map((op, idx) => (
              <SharedStyled.FlexRow
                onClick={() => navigate(`/${getEnumValue(op?.stage?.stageGroup)}/opportunity/${op?._id}`)}
                style={{ cursor: 'pointer' }}
                key={idx}
                justifyContent="center"
                gap="8px"
              >
                <SharedStyled.Text fontSize="16px" fontWeight="700" color="#000">
                  {op.PO}-{op.num}
                </SharedStyled.Text>
                <SharedStyled.Text fontSize="16px" fontWeight="700" color="#999">
                  {op?.oppType?.name || '--'}
                </SharedStyled.Text>
              </SharedStyled.FlexRow>
            ))}

            <SharedStyled.Text fontSize="16px" fontWeight="600" textAlign="center" color="#333">
              Click to go to an opportunity above
              {/* , this will create a comment with all info entered so far */}
            </SharedStyled.Text>

            <SharedStyled.Text fontSize="16px" fontWeight="600" textAlign="center" color="#333">
              OR
            </SharedStyled.Text>

            <SharedStyled.Text fontSize="16px" fontWeight="600" textAlign="center" color="#333">
              Continue creating this opportunity below
            </SharedStyled.Text>

            <Button type="button" onClick={onConfirm}>
              Continue
            </Button>
          </SharedStyled.FlexCol>
        </SharedStyled.Content>
      </SharedStyled.SettingModalContentContainer>
    </div>
  )
}

export default FoundOpportunity
