import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { dayjsFormat, formatPhoneNumber, getOrdinal, notify } from '../../../../../shared/helpers/util'
import Button from '../../../../../shared/components/button/Button'
import { colors } from '../../../../../styles/theme'
import { Types } from '../../../constant'

const ModalContainer = styled.div`
  background: ${colors.white};
  border-radius: 10px;
  padding: 30px;
  width: 95vw;
  max-width: 1200px;
`

const ModalTitle = styled.h2`
  margin: 0 0 30px 0;
  color: ${colors.darkGrey};
  text-align: center;
  font-size: 24px;
  font-weight: bold;
`
const TableWrapper = styled.div`
  overflow-x: auto;
  width: 100%;
`

const ComparisonTable = styled.table`
  width: max-content;
  border-collapse: collapse;
  margin-bottom: 30px;
  border: 1px solid ${colors.lightGrey3};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
`

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: center;
  background-color: ${colors.lightGrey2};
  color: ${colors.darkGrey};
  font-weight: 600;
  min-width: 150px;
  border: 1px solid ${colors.lightGrey3};
  border-bottom: 2px solid ${colors.lightGrey3};
`

const TableCell = styled.td`
  padding: 12px 15px;
  border: 1px solid ${colors.lightGrey3};
  text-align: left;
  vertical-align: middle;
  max-width: 280px;
  input[type='radio'] {
    margin-right: 8px;
    vertical-align: middle;
  }
`

const FieldLabel = styled.td`
  padding: 12px 15px;
  font-weight: 600;
  background-color: ${colors.lightGrey1};
  width: 80px;
  border: 1px solid ${colors.lightGrey3};
  vertical-align: middle;
`

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
`

interface ContactMatchModalProps {
  existingContacts: any[]
  newContactData: any
  onClose: () => void
  onMerge: (data: { mergedFields: Record<string, any>; toContact: string }) => void
  isNewContact?: boolean
}

const ContactMergeModal: React.FC<ContactMatchModalProps> = ({
  existingContacts = [],
  newContactData = {},
  onClose,
  onMerge,
  isNewContact,
}) => {
  const [selectedFields, setSelectedFields] = useState<Record<string, any>>({})
  const [selectedFieldSources, setSelectedFieldSources] = useState<Record<string, string>>({})
  const [selectedContactIndex, setSelectedContactIndex] = useState<number>(-1)

  const isBusiness = newContactData?.isBusiness
  const allFields = [
    ...(isBusiness ? ['businessName'] : []),
    'fullName',
    'phone',
    'email',
    'fullAddress',
    'nextAction',
    'type',
  ]

  useEffect(() => {
    handleSelectContact(-1) // Default: prefill with user-entered data
  }, [newContactData])

  const handleFieldSelect = (field: string, value: any, sourceKey: string) => {
    if (field === 'nextAction') {
      setSelectedFields((prev) => ({ ...prev, nextAction: value }))
    } else if (field === 'fullAddress') {
      setSelectedFields((prev) => ({
        ...prev,
        street: value.street,
        city: value.city,
        state: value.state,
        zip: value.zip,
        fullAddress: value.fullAddress,
      }))
    } else if (field === 'fullName') {
      setSelectedFields((prev) => ({
        ...prev,
        fullName: value.fullName,
        firstName: value.firstName,
        lastName: value.lastName,
      }))
    } else {
      setSelectedFields((prev) => ({ ...prev, [field]: value }))
    }
    setSelectedFieldSources((prev) => ({ ...prev, [field]: sourceKey }))
  }

  const renderRadio = (field: string, value: any, sourceKey: string) => {
    console.log({ value })
    const isEmptyValue = () => {
      if (value === '' || value === undefined || value === null) return true

      if (typeof value === 'object' && value !== null) {
        return Object.values(value).every((v) => v === '' || v === undefined || v === null)
      }

      return false
    }

    if (isEmptyValue()) {
      return <span style={{ width: '20px', display: 'inline-block' }} />
    }

    return (
      <input
        type="radio"
        name={`radio-${field}`}
        checked={selectedFieldSources[field] === sourceKey}
        onChange={() => handleFieldSelect(field, value, sourceKey)}
      />
    )
  }

  const handleSelectContact = (index: number) => {
    setSelectedContactIndex(index)
    const contact = index === -1 ? newContactData : existingContacts[index]
    const prefix = index === -1 ? 'you-entered' : `existing-${index}`

    const fields: Record<string, any> = {}
    const sources: Record<string, string> = {}

    allFields.forEach((field) => {
      const value = contact[field]

      if (field === 'fullAddress' && contact.fullAddress) {
        fields.street = contact.street || ''
        fields.city = contact.city || ''
        fields.state = contact.state || ''
        fields.zip = contact.zip || ''
        fields.fullAddress = contact.fullAddress || ''

        sources.street = prefix
        sources.city = prefix
        sources.state = prefix
        sources.zip = prefix
        sources.fullAddress = prefix
      } else if (field === 'fullName' && contact.fullName) {
        fields.fullName = contact.fullName
        fields.firstName = contact.firstName
        fields.lastName = contact.lastName

        sources.fullName = prefix
        sources.firstName = prefix
        sources.lastName = prefix
      } else if (field === 'nextAction' && contact.nextAction) {
        fields.nextAction = contact.nextAction
        sources.nextAction = prefix
      } else if (value) {
        fields[field] = value
        sources[field] = prefix
      }
    })

    setSelectedFields(fields)
    setSelectedFieldSources(sources)
  }

  const handleMergeClick = () => {
    const toContact = selectedContactIndex >= 0 ? existingContacts[selectedContactIndex]._id : undefined
    console.log({
      mergedFields: {
        ...selectedFields,
        fromContact: existingContacts.map((contact) => contact._id).filter((id) => id && id !== ''),
      },
      toContact: newContactData._id,
    })

    onMerge({
      mergedFields: {
        ...selectedFields,
        fromContact: existingContacts.map((contact) => contact._id).filter((id) => id && id !== ''),
      },
      toContact: newContactData._id,
    })
  }

  return (
    <ModalContainer>
      <ModalTitle>Contact Match Found</ModalTitle>

      <TableWrapper>
        <ComparisonTable>
          <thead>
            <tr>
              <TableHeader>Field</TableHeader>
              <TableHeader>
                <input
                  type="radio"
                  name="rowSelect"
                  checked={selectedContactIndex === -1}
                  onChange={() => handleSelectContact(-1)}
                />
                Primary Contact
              </TableHeader>
              {existingContacts.map((contact, index) => (
                <TableHeader key={contact._id}>
                  <input
                    type="radio"
                    name="rowSelect"
                    checked={selectedContactIndex === index}
                    onChange={() => handleSelectContact(index)}
                  />
                  {getOrdinal(index + 1)} Contact To Merge
                </TableHeader>
              ))}
            </tr>
          </thead>
          <tbody>
            {allFields.map((field) => (
              <tr key={field}>
                <FieldLabel>
                  {{
                    fullName: 'Full Name',
                    businessName: 'Business Name',
                    phone: 'Phone',
                    email: 'Email',
                    fullAddress: 'Address',
                    nextAction: 'Action',
                    type: 'Type',
                  }[field] || field}
                </FieldLabel>
                <TableCell>
                  {renderRadio(
                    field,
                    field === 'fullAddress'
                      ? {
                          street: newContactData.street,
                          city: newContactData.city,
                          state: newContactData.state,
                          zip: newContactData.zip,
                          fullAddress: newContactData.fullAddress,
                        }
                      : field === 'fullName'
                      ? {
                          firstName: newContactData.firstName,
                          lastName: newContactData.lastName,
                          fullName: newContactData.fullName,
                        }
                      : newContactData[field],
                    'you-entered'
                  )}
                  {field === 'phone'
                    ? formatPhoneNumber(newContactData[field], '')
                    : field === 'nextAction'
                    ? `${newContactData[field]?.body || ''} ${newContactData[field]?.body ? '-' : ''} ${dayjsFormat(
                        newContactData[field]?.createdAt,
                        'M/D/YY'
                      )}`
                    : field === 'type'
                    ? Object.entries(Types).find(([_, v]) => v === newContactData[field])?.[0] || ''
                    : newContactData[field]}
                </TableCell>
                {existingContacts.map((contact, idx) => (
                  <TableCell key={contact._id}>
                    {renderRadio(
                      field,
                      field === 'fullAddress'
                        ? {
                            street: contact.street,
                            city: contact.city,
                            state: contact.state,
                            zip: contact.zip,
                            fullAddress: contact.fullAddress,
                          }
                        : field === 'fullName'
                        ? {
                            firstName: contact.firstName,
                            lastName: contact.lastName,
                            fullName: contact.fullName,
                          }
                        : contact[field],
                      `existing-${idx}`
                    )}
                    {field === 'phone'
                      ? formatPhoneNumber(contact[field], '')
                      : field === 'nextAction'
                      ? `${contact[field]?.body || ''} ${contact[field]?.body ? '-' : ''} ${dayjsFormat(
                          contact[field]?.createdAt,
                          'M/D/YY'
                        )}`
                      : field === 'type'
                      ? Object.entries(Types).find(([_, v]) => v === contact[field])?.[0] || ''
                      : contact[field]}
                  </TableCell>
                ))}
              </tr>
            ))}
          </tbody>
        </ComparisonTable>
      </TableWrapper>

      <ButtonContainer>
        <Button onClick={onClose} className="gray" width="48%">
          {isNewContact ? 'Create New Contact' : 'Cancel'}
        </Button>
        <Button onClick={handleMergeClick} className="orange" width="48%">
          Merge Contacts
        </Button>
      </ButtonContainer>
    </ModalContainer>
  )
}

export default ContactMergeModal
